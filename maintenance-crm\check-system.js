#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('\n========================================');
console.log('   فحص حالة نظام إدارة الصيانة');
console.log('========================================\n');

let hasErrors = false;

// فحص Node.js
console.log('🔍 فحص Node.js...');
const nodeVersion = process.version;
const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);

if (majorVersion >= 18) {
    console.log(`✅ Node.js ${nodeVersion} - مدعوم`);
} else {
    console.log(`❌ Node.js ${nodeVersion} - يجب استخدام الإصدار 18 أو أحدث`);
    hasErrors = true;
}

// فحص package.json
console.log('\n🔍 فحص package.json...');
if (fs.existsSync('package.json')) {
    console.log('✅ package.json موجود');
    
    try {
        const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
        
        // فحص التبعيات المهمة
        const requiredDeps = [
            'next',
            'react',
            'react-dom',
            'typescript',
            '@supabase/supabase-js',
            'tailwindcss'
        ];
        
        console.log('\n🔍 فحص التبعيات الأساسية...');
        requiredDeps.forEach(dep => {
            if (packageJson.dependencies?.[dep] || packageJson.devDependencies?.[dep]) {
                console.log(`✅ ${dep} - موجود`);
            } else {
                console.log(`❌ ${dep} - مفقود`);
                hasErrors = true;
            }
        });
        
    } catch (error) {
        console.log('❌ خطأ في قراءة package.json');
        hasErrors = true;
    }
} else {
    console.log('❌ package.json غير موجود');
    hasErrors = true;
}

// فحص node_modules
console.log('\n🔍 فحص node_modules...');
if (fs.existsSync('node_modules')) {
    console.log('✅ node_modules موجود');
} else {
    console.log('⚠️  node_modules غير موجود - قم بتشغيل: npm install');
}

// فحص ملف البيئة
console.log('\n🔍 فحص ملف البيئة...');
if (fs.existsSync('.env.local')) {
    console.log('✅ .env.local موجود');
    
    try {
        const envContent = fs.readFileSync('.env.local', 'utf8');
        
        if (envContent.includes('NEXT_PUBLIC_SUPABASE_URL=')) {
            const urlMatch = envContent.match(/NEXT_PUBLIC_SUPABASE_URL=(.+)/);
            if (urlMatch && urlMatch[1] && urlMatch[1].trim() !== 'your_supabase_project_url') {
                console.log('✅ SUPABASE_URL مُعرَّف');
            } else {
                console.log('⚠️  SUPABASE_URL غير مُعرَّف بشكل صحيح');
            }
        } else {
            console.log('❌ SUPABASE_URL مفقود');
            hasErrors = true;
        }
        
        if (envContent.includes('NEXT_PUBLIC_SUPABASE_ANON_KEY=')) {
            const keyMatch = envContent.match(/NEXT_PUBLIC_SUPABASE_ANON_KEY=(.+)/);
            if (keyMatch && keyMatch[1] && keyMatch[1].trim() !== 'your_supabase_anon_key') {
                console.log('✅ SUPABASE_ANON_KEY مُعرَّف');
            } else {
                console.log('⚠️  SUPABASE_ANON_KEY غير مُعرَّف بشكل صحيح');
            }
        } else {
            console.log('❌ SUPABASE_ANON_KEY مفقود');
            hasErrors = true;
        }
        
    } catch (error) {
        console.log('❌ خطأ في قراءة .env.local');
        hasErrors = true;
    }
} else {
    console.log('⚠️  .env.local غير موجود');
    console.log('   قم بنسخ .env.example إلى .env.local وأضف بيانات Supabase');
}

// فحص الملفات الأساسية
console.log('\n🔍 فحص الملفات الأساسية...');
const requiredFiles = [
    'next.config.ts',
    'tailwind.config.js',
    'tsconfig.json',
    'src/app/page.tsx',
    'src/lib/supabase.ts',
    'database/schema.sql'
];

requiredFiles.forEach(file => {
    if (fs.existsSync(file)) {
        console.log(`✅ ${file} - موجود`);
    } else {
        console.log(`❌ ${file} - مفقود`);
        hasErrors = true;
    }
});

// فحص مجلدات المكونات
console.log('\n🔍 فحص مجلدات المكونات...');
const requiredDirs = [
    'src/app',
    'src/components',
    'src/lib',
    'src/types',
    'database'
];

requiredDirs.forEach(dir => {
    if (fs.existsSync(dir)) {
        console.log(`✅ ${dir} - موجود`);
    } else {
        console.log(`❌ ${dir} - مفقود`);
        hasErrors = true;
    }
});

// النتيجة النهائية
console.log('\n========================================');
if (hasErrors) {
    console.log('❌ يوجد مشاكل في النظام');
    console.log('\nيرجى مراجعة ملف SETUP.md لحل المشاكل');
    process.exit(1);
} else {
    console.log('✅ النظام جاهز للتشغيل!');
    console.log('\nلتشغيل النظام:');
    console.log('  Windows: start.bat');
    console.log('  Mac/Linux: ./start.sh');
    console.log('  أو: npm run dev');
}
console.log('========================================\n');
