'use client'

import { useState, useEffect } from 'react'
import { ServiceRequest } from '@/types'
import { ServiceRequestList } from '@/components/service-requests/service-request-list'
import { ServiceRequestForm } from '@/components/service-requests/service-request-form'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ArrowRight, ClipboardList, AlertCircle, Clock, CheckCircle } from 'lucide-react'
import { ServiceRequestsAPI } from '@/lib/api/service-requests'
import Link from 'next/link'

type ViewMode = 'list' | 'add' | 'edit' | 'view'

export default function ServiceRequestsPage() {
  const [serviceRequests, setServiceRequests] = useState<ServiceRequest[]>([])
  const [selectedRequest, setSelectedRequest] = useState<ServiceRequest | null>(null)
  const [viewMode, setViewMode] = useState<ViewMode>('list')
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [statistics, setStatistics] = useState({
    total: 0,
    pending: 0,
    inProgress: 0,
    completed: 0,
    urgent: 0
  })

  // Load service requests data
  useEffect(() => {
    loadServiceRequests()
  }, [])

  const loadServiceRequests = async () => {
    setIsLoading(true)
    setError(null)
    
    const result = await ServiceRequestsAPI.getAll()
    
    if (result.error) {
      setError(result.error)
      // Fallback to mock data for development
      const mockRequests: ServiceRequest[] = [
        {
          id: '1',
          customer_id: '1',
          device_id: '1',
          request_date: '2025-01-10T08:30:00Z',
          received_by: 'سارة أحمد - خدمة العملاء',
          request_source: 'هاتف',
          initial_description: 'المكيف لا يبرد بشكل جيد ويصدر أصوات غريبة عند التشغيل',
          priority: 'عاجل',
          status: 'تم تحديد موعد',
          created_at: '2025-01-10T08:30:00Z',
          updated_at: '2025-01-10T08:30:00Z',
          customer: {
            id: '1',
            full_name: 'شركة الرياض للتجارة',
            primary_phone: '+966112345678',
            secondary_phone: '+966112345679',
            email: '<EMAIL>',
            detailed_address: 'شارع الملك فهد، حي العليا، الرياض',
            landmark: 'بجانب برج المملكة',
            contact_person: 'أحمد المدير العام',
            special_notes: 'يفضل الاتصال صباحاً',
            created_at: '2024-01-15T10:00:00Z',
            updated_at: '2024-01-15T10:00:00Z'
          },
          device: {
            id: '1',
            customer_id: '1',
            device_name: 'مكيف سبليت',
            model: 'Samsung AR24',
            serial_number: 'SAM2024001',
            purchase_date: '2024-01-15',
            warranty_end_date: '2026-01-15',
            original_invoice_number: 'INV-2024-001',
            created_at: '2024-01-15T10:00:00Z',
            updated_at: '2024-01-15T10:00:00Z'
          }
        },
        {
          id: '2',
          customer_id: '2',
          device_id: '2',
          request_date: '2025-01-09T14:15:00Z',
          received_by: 'محمد علي - خدمة العملاء',
          request_source: 'بريد إلكتروني',
          initial_description: 'الغسالة لا تعصر الملابس بشكل جيد وتترك الملابس مبللة',
          priority: 'متوسط',
          status: 'في انتظار فني',
          created_at: '2025-01-09T14:15:00Z',
          updated_at: '2025-01-09T14:15:00Z',
          customer: {
            id: '2',
            full_name: 'عبدالرحمن محمد الغامدي',
            primary_phone: '+966501111111',
            secondary_phone: '+966502222222',
            email: '<EMAIL>',
            detailed_address: 'حي النرجس، شارع التخصصي، الرياض',
            landmark: 'فيلا رقم 123',
            contact_person: undefined,
            special_notes: 'لديه حساسية من الغبار',
            created_at: '2024-02-10T14:30:00Z',
            updated_at: '2024-02-10T14:30:00Z'
          },
          device: {
            id: '2',
            customer_id: '2',
            device_name: 'غسالة ملابس',
            model: 'Bosch WAT28',
            serial_number: 'BOS2024004',
            purchase_date: '2024-04-05',
            warranty_end_date: '2026-04-05',
            original_invoice_number: 'INV-2024-004',
            created_at: '2024-04-05T10:00:00Z',
            updated_at: '2024-04-05T10:00:00Z'
          }
        }
      ]
      setServiceRequests(mockRequests)
      calculateStatistics(mockRequests)
    } else {
      setServiceRequests(result.data || [])
      calculateStatistics(result.data || [])
    }
    
    setIsLoading(false)
  }

  const calculateStatistics = (requests: ServiceRequest[]) => {
    const stats = {
      total: requests.length,
      pending: requests.filter(r => ['قيد المراجعة', 'تم تسجيله', 'في انتظار فني'].includes(r.status)).length,
      inProgress: requests.filter(r => ['تم تحديد موعد', 'في الطريق للعميل', 'قيد التنفيذ'].includes(r.status)).length,
      completed: requests.filter(r => ['مكتمل', 'مغلق'].includes(r.status)).length,
      urgent: requests.filter(r => r.priority === 'عاجل').length
    }
    setStatistics(stats)
  }

  const handleAddRequest = async (data: any) => {
    const result = await ServiceRequestsAPI.create(data)
    
    if (result.error) {
      alert(`خطأ: ${result.error}`)
      return
    }
    
    if (result.data) {
      setServiceRequests(prev => [result.data!, ...prev])
      calculateStatistics([result.data!, ...serviceRequests])
      setViewMode('list')
      alert(result.message || 'تم إنشاء طلب الصيانة بنجاح!')
    }
  }

  const handleEditRequest = async (data: any) => {
    if (!selectedRequest) return
    
    const result = await ServiceRequestsAPI.update(selectedRequest.id, data)
    
    if (result.error) {
      alert(`خطأ: ${result.error}`)
      return
    }
    
    if (result.data) {
      const updatedRequests = serviceRequests.map(request => 
        request.id === selectedRequest.id ? result.data! : request
      )
      setServiceRequests(updatedRequests)
      calculateStatistics(updatedRequests)
      setSelectedRequest(result.data)
      setViewMode('view')
      alert(result.message || 'تم تحديث طلب الصيانة بنجاح!')
    }
  }

  const handleViewRequest = (request: ServiceRequest) => {
    setSelectedRequest(request)
    setViewMode('view')
  }

  const handleEditMode = (request: ServiceRequest) => {
    setSelectedRequest(request)
    setViewMode('edit')
  }

  const handleBackToList = () => {
    setSelectedRequest(null)
    setViewMode('list')
  }

  const renderContent = () => {
    switch (viewMode) {
      case 'add':
        return (
          <ServiceRequestForm
            onSubmit={handleAddRequest}
            onCancel={handleBackToList}
          />
        )
      
      case 'edit':
        return selectedRequest ? (
          <ServiceRequestForm
            initialData={selectedRequest}
            onSubmit={handleEditRequest}
            onCancel={handleBackToList}
          />
        ) : null
      
      case 'view':
        // سيتم تطوير مكون عرض تفاصيل طلب الصيانة لاحقاً
        return (
          <div className="text-center py-12">
            <ClipboardList className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              عرض تفاصيل طلب الصيانة
            </h3>
            <p className="text-gray-500 mb-4">
              سيتم تطوير هذه الميزة قريباً
            </p>
            <Button onClick={handleBackToList}>
              العودة للقائمة
            </Button>
          </div>
        )
      
      default:
        return (
          <ServiceRequestList
            serviceRequests={serviceRequests}
            onEdit={handleEditMode}
            onView={handleViewRequest}
            onAdd={() => setViewMode('add')}
            isLoading={isLoading}
          />
        )
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center gap-4">
              {viewMode !== 'list' && (
                <Button
                  variant="ghost"
                  onClick={handleBackToList}
                  className="flex items-center gap-2"
                >
                  <ArrowRight className="h-4 w-4" />
                  العودة للقائمة
                </Button>
              )}
              <div className="flex items-center gap-3">
                <ClipboardList className="h-8 w-8 text-blue-600" />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    {viewMode === 'add' && 'طلب صيانة جديد'}
                    {viewMode === 'edit' && 'تعديل طلب الصيانة'}
                    {viewMode === 'view' && 'تفاصيل طلب الصيانة'}
                    {viewMode === 'list' && 'طلبات الصيانة'}
                  </h1>
                  <p className="text-gray-600">
                    {viewMode === 'add' && 'إنشاء طلب صيانة جديد'}
                    {viewMode === 'edit' && 'تعديل بيانات طلب الصيانة'}
                    {viewMode === 'view' && selectedRequest?.initial_description}
                    {viewMode === 'list' && 'عرض وإدارة جميع طلبات الصيانة'}
                  </p>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <Link href="/">
                <Button variant="outline">
                  العودة للرئيسية
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Statistics Cards - only show in list view */}
        {viewMode === 'list' && !isLoading && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">إجمالي الطلبات</CardTitle>
                <ClipboardList className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{statistics.total}</div>
                <p className="text-xs text-muted-foreground">طلب صيانة</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">في الانتظار</CardTitle>
                <Clock className="h-4 w-4 text-yellow-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">{statistics.pending}</div>
                <p className="text-xs text-muted-foreground">طلب معلق</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">قيد التنفيذ</CardTitle>
                <Clock className="h-4 w-4 text-blue-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">{statistics.inProgress}</div>
                <p className="text-xs text-muted-foreground">طلب قيد التنفيذ</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">مكتملة</CardTitle>
                <CheckCircle className="h-4 w-4 text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{statistics.completed}</div>
                <p className="text-xs text-muted-foreground">طلب مكتمل</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">عاجلة</CardTitle>
                <AlertCircle className="h-4 w-4 text-red-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">{statistics.urgent}</div>
                <p className="text-xs text-muted-foreground">طلب عاجل</p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Error Message */}
        {error && viewMode === 'list' && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-yellow-600" />
              <p className="text-yellow-800">
                تعذر الاتصال بقاعدة البيانات. يتم عرض بيانات تجريبية.
              </p>
            </div>
          </div>
        )}

        {/* Main Content Area */}
        {renderContent()}
      </div>
    </div>
  )
}
