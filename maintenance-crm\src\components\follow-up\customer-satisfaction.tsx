'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { 
  Star, 
  Phone, 
  MessageSquare, 
  ThumbsUp, 
  ThumbsDown,
  Clock,
  User,
  CheckCircle,
  AlertCircle,
  TrendingUp,
  TrendingDown
} from 'lucide-react'

interface CustomerFeedback {
  id: string
  execution_id: string
  customer_name: string
  service_date: string
  engineer_name: string
  rating: number
  feedback_text?: string
  follow_up_status: 'pending' | 'contacted' | 'resolved' | 'no_action_needed'
  follow_up_notes?: string
  created_at: string
  service_type: string
  response_time: number
}

interface SatisfactionStats {
  averageRating: number
  totalFeedbacks: number
  ratingDistribution: Record<number, number>
  responseRate: number
  pendingFollowUps: number
}

export function CustomerSatisfaction() {
  const [feedbacks, setFeedbacks] = useState<CustomerFeedback[]>([])
  const [stats, setStats] = useState<SatisfactionStats>({
    averageRating: 0,
    totalFeedbacks: 0,
    ratingDistribution: {},
    responseRate: 0,
    pendingFollowUps: 0
  })
  const [selectedFeedback, setSelectedFeedback] = useState<CustomerFeedback | null>(null)
  const [followUpNotes, setFollowUpNotes] = useState('')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadFeedbacks()
  }, [])

  const loadFeedbacks = async () => {
    setIsLoading(true)
    
    // محاكاة تحميل البيانات
    setTimeout(() => {
      const mockFeedbacks: CustomerFeedback[] = [
        {
          id: '1',
          execution_id: '1',
          customer_name: 'شركة الرياض للتجارة',
          service_date: '2025-01-15T12:30:00Z',
          engineer_name: 'أحمد محمد العلي',
          rating: 5,
          feedback_text: 'خدمة ممتازة والمهندس محترف جداً. تم إصلاح المكيف بسرعة وكفاءة عالية.',
          follow_up_status: 'no_action_needed',
          created_at: '2025-01-15T14:00:00Z',
          service_type: 'صيانة مكيف',
          response_time: 4.5
        },
        {
          id: '2',
          execution_id: '2',
          customer_name: 'محمد أحمد السعيد',
          service_date: '2025-01-14T10:00:00Z',
          engineer_name: 'سارة محمد',
          rating: 3,
          feedback_text: 'الخدمة جيدة لكن تأخر المهندس عن الموعد المحدد بساعة.',
          follow_up_status: 'pending',
          created_at: '2025-01-14T16:30:00Z',
          service_type: 'إصلاح ثلاجة',
          response_time: 6.0
        },
        {
          id: '3',
          execution_id: '3',
          customer_name: 'مؤسسة النور التجارية',
          service_date: '2025-01-13T09:00:00Z',
          engineer_name: 'خالد عبدالله',
          rating: 4,
          feedback_text: 'خدمة جيدة والمهندس ماهر، لكن كان بحاجة لقطع غيار إضافية.',
          follow_up_status: 'contacted',
          follow_up_notes: 'تم الاتصال بالعميل وتوضيح سبب الحاجة لقطع الغيار الإضافية',
          created_at: '2025-01-13T15:00:00Z',
          service_type: 'صيانة غسالة',
          response_time: 3.5
        },
        {
          id: '4',
          execution_id: '4',
          customer_name: 'فاطمة علي الزهراني',
          service_date: '2025-01-12T14:00:00Z',
          engineer_name: 'أحمد محمد العلي',
          rating: 2,
          feedback_text: 'المشكلة لم تحل بشكل كامل وعاد العطل بعد يومين.',
          follow_up_status: 'pending',
          created_at: '2025-01-14T10:00:00Z',
          service_type: 'إصلاح مكيف',
          response_time: 5.0
        }
      ]

      setFeedbacks(mockFeedbacks)
      
      // حساب الإحصائيات
      const totalFeedbacks = mockFeedbacks.length
      const averageRating = mockFeedbacks.reduce((sum, f) => sum + f.rating, 0) / totalFeedbacks
      const ratingDistribution = mockFeedbacks.reduce((acc, f) => {
        acc[f.rating] = (acc[f.rating] || 0) + 1
        return acc
      }, {} as Record<number, number>)
      const pendingFollowUps = mockFeedbacks.filter(f => f.follow_up_status === 'pending').length
      
      setStats({
        averageRating: Math.round(averageRating * 10) / 10,
        totalFeedbacks,
        ratingDistribution,
        responseRate: 85, // محاكاة معدل الاستجابة
        pendingFollowUps
      })
      
      setIsLoading(false)
    }, 1000)
  }

  const updateFollowUpStatus = async (feedbackId: string, status: CustomerFeedback['follow_up_status'], notes?: string) => {
    setFeedbacks(prev => prev.map(feedback => 
      feedback.id === feedbackId 
        ? { ...feedback, follow_up_status: status, follow_up_notes: notes }
        : feedback
    ))
    
    // تحديث الإحصائيات
    const updatedPendingCount = feedbacks.filter(f => 
      f.id === feedbackId ? status === 'pending' : f.follow_up_status === 'pending'
    ).length
    
    setStats(prev => ({ ...prev, pendingFollowUps: updatedPendingCount }))
    setSelectedFeedback(null)
    setFollowUpNotes('')
  }

  const getRatingColor = (rating: number) => {
    if (rating >= 4) return 'text-green-600'
    if (rating >= 3) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getStatusColor = (status: CustomerFeedback['follow_up_status']) => {
    const colors = {
      pending: 'bg-yellow-100 text-yellow-800',
      contacted: 'bg-blue-100 text-blue-800',
      resolved: 'bg-green-100 text-green-800',
      no_action_needed: 'bg-gray-100 text-gray-800'
    }
    return colors[status]
  }

  const getStatusText = (status: CustomerFeedback['follow_up_status']) => {
    const texts = {
      pending: 'في انتظار المتابعة',
      contacted: 'تم الاتصال',
      resolved: 'تم الحل',
      no_action_needed: 'لا يحتاج متابعة'
    }
    return texts[status]
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ))
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="space-y-3">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Statistics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">متوسط التقييم</CardTitle>
            <Star className="h-4 w-4 text-yellow-400" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getRatingColor(stats.averageRating)}`}>
              {stats.averageRating}/5
            </div>
            <div className="flex items-center mt-2">
              {renderStars(Math.round(stats.averageRating))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي التقييمات</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalFeedbacks}</div>
            <p className="text-xs text-muted-foreground">
              معدل الاستجابة: {stats.responseRate}%
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">متابعات معلقة</CardTitle>
            <Clock className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">
              {stats.pendingFollowUps}
            </div>
            <p className="text-xs text-muted-foreground">
              يحتاج اهتمام فوري
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">التقييمات الإيجابية</CardTitle>
            <ThumbsUp className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {Math.round(((stats.ratingDistribution[4] || 0) + (stats.ratingDistribution[5] || 0)) / stats.totalFeedbacks * 100)}%
            </div>
            <p className="text-xs text-muted-foreground">
              تقييم 4 نجوم أو أكثر
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Rating Distribution */}
      <Card>
        <CardHeader>
          <CardTitle>توزيع التقييمات</CardTitle>
          <CardDescription>توزيع تقييمات العملاء حسب عدد النجوم</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[5, 4, 3, 2, 1].map((rating) => {
              const count = stats.ratingDistribution[rating] || 0
              const percentage = stats.totalFeedbacks > 0 ? (count / stats.totalFeedbacks) * 100 : 0
              
              return (
                <div key={rating} className="flex items-center gap-4">
                  <div className="flex items-center gap-1 w-20">
                    <span className="text-sm font-medium">{rating}</span>
                    <Star className="h-3 w-3 text-yellow-400 fill-current" />
                  </div>
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${getRatingColor(rating).replace('text-', 'bg-')}`}
                      style={{ width: `${percentage}%` }}
                    />
                  </div>
                  <div className="text-sm text-gray-600 w-16 text-left">
                    {count} ({percentage.toFixed(0)}%)
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Customer Feedbacks */}
      <Card>
        <CardHeader>
          <CardTitle>تقييمات العملاء</CardTitle>
          <CardDescription>آراء وتقييمات العملاء للخدمات المقدمة</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {feedbacks.map((feedback) => (
              <div key={feedback.id} className="border rounded-lg p-4 space-y-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="font-medium text-gray-900">{feedback.customer_name}</h3>
                      <Badge className={getStatusColor(feedback.follow_up_status)}>
                        {getStatusText(feedback.follow_up_status)}
                      </Badge>
                    </div>
                    
                    <div className="flex items-center gap-4 text-sm text-gray-600 mb-2">
                      <div className="flex items-center gap-1">
                        <User className="h-3 w-3" />
                        {feedback.engineer_name}
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {new Date(feedback.service_date).toLocaleDateString('ar-SA')}
                      </div>
                      <div>{feedback.service_type}</div>
                    </div>

                    <div className="flex items-center gap-2 mb-3">
                      {renderStars(feedback.rating)}
                      <span className={`font-medium ${getRatingColor(feedback.rating)}`}>
                        {feedback.rating}/5
                      </span>
                    </div>

                    {feedback.feedback_text && (
                      <p className="text-gray-700 bg-gray-50 rounded p-3 mb-3">
                        "{feedback.feedback_text}"
                      </p>
                    )}

                    {feedback.follow_up_notes && (
                      <div className="bg-blue-50 border border-blue-200 rounded p-3">
                        <p className="text-sm text-blue-800">
                          <strong>ملاحظات المتابعة:</strong> {feedback.follow_up_notes}
                        </p>
                      </div>
                    )}
                  </div>

                  <div className="flex flex-col gap-2 ml-4">
                    {feedback.follow_up_status === 'pending' && (
                      <Button
                        size="sm"
                        onClick={() => setSelectedFeedback(feedback)}
                        className="whitespace-nowrap"
                      >
                        <Phone className="h-3 w-3 ml-1" />
                        متابعة
                      </Button>
                    )}
                    
                    {feedback.rating <= 3 && feedback.follow_up_status !== 'resolved' && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => updateFollowUpStatus(feedback.id, 'resolved')}
                        className="whitespace-nowrap"
                      >
                        <CheckCircle className="h-3 w-3 ml-1" />
                        تم الحل
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Follow-up Modal */}
      {selectedFeedback && (
        <Card className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">متابعة تقييم العميل</h3>
            
            <div className="space-y-4">
              <div>
                <p className="text-sm text-gray-600">العميل: {selectedFeedback.customer_name}</p>
                <p className="text-sm text-gray-600">التقييم: {selectedFeedback.rating}/5</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  ملاحظات المتابعة
                </label>
                <Textarea
                  value={followUpNotes}
                  onChange={(e) => setFollowUpNotes(e.target.value)}
                  placeholder="اكتب ملاحظات المتابعة..."
                  rows={3}
                />
              </div>

              <div className="flex gap-3">
                <Button
                  onClick={() => updateFollowUpStatus(selectedFeedback.id, 'contacted', followUpNotes)}
                  className="flex-1"
                >
                  تم الاتصال
                </Button>
                <Button
                  onClick={() => updateFollowUpStatus(selectedFeedback.id, 'resolved', followUpNotes)}
                  variant="outline"
                  className="flex-1"
                >
                  تم الحل
                </Button>
                <Button
                  onClick={() => setSelectedFeedback(null)}
                  variant="outline"
                >
                  إلغاء
                </Button>
              </div>
            </div>
          </div>
        </Card>
      )}
    </div>
  )
}
