import { supabase } from '@/lib/supabase'
import { Customer, CustomerFormData, ApiResponse } from '@/types'

export class CustomersAPI {
  // Get all customers
  static async getAll(): Promise<ApiResponse<Customer[]>> {
    try {
      const { data, error } = await supabase
        .from('customers')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching customers:', error)
        return { error: 'فشل في جلب بيانات العملاء' }
      }

      return { data: data || [] }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Get customer by ID
  static async getById(id: string): Promise<ApiResponse<Customer>> {
    try {
      const { data, error } = await supabase
        .from('customers')
        .select('*')
        .eq('id', id)
        .single()

      if (error) {
        console.error('Error fetching customer:', error)
        return { error: 'فشل في جلب بيانات العميل' }
      }

      if (!data) {
        return { error: 'العميل غير موجود' }
      }

      return { data }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Create new customer
  static async create(customerData: CustomerFormData): Promise<ApiResponse<Customer>> {
    try {
      // Check if phone number already exists
      const { data: existingCustomer } = await supabase
        .from('customers')
        .select('id')
        .eq('primary_phone', customerData.primary_phone)
        .single()

      if (existingCustomer) {
        return { error: 'رقم الهاتف مسجل مسبقاً لعميل آخر' }
      }

      const { data, error } = await supabase
        .from('customers')
        .insert([customerData])
        .select()
        .single()

      if (error) {
        console.error('Error creating customer:', error)
        return { error: 'فشل في إنشاء العميل' }
      }

      return { 
        data, 
        message: 'تم إنشاء العميل بنجاح' 
      }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Update customer
  static async update(id: string, customerData: Partial<CustomerFormData>): Promise<ApiResponse<Customer>> {
    try {
      // If updating phone number, check if it's already used by another customer
      if (customerData.primary_phone) {
        const { data: existingCustomer } = await supabase
          .from('customers')
          .select('id')
          .eq('primary_phone', customerData.primary_phone)
          .neq('id', id)
          .single()

        if (existingCustomer) {
          return { error: 'رقم الهاتف مسجل مسبقاً لعميل آخر' }
        }
      }

      const { data, error } = await supabase
        .from('customers')
        .update(customerData)
        .eq('id', id)
        .select()
        .single()

      if (error) {
        console.error('Error updating customer:', error)
        return { error: 'فشل في تحديث بيانات العميل' }
      }

      return { 
        data, 
        message: 'تم تحديث بيانات العميل بنجاح' 
      }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Delete customer
  static async delete(id: string): Promise<ApiResponse<void>> {
    try {
      // Check if customer has any devices or service requests
      const { data: devices } = await supabase
        .from('devices')
        .select('id')
        .eq('customer_id', id)
        .limit(1)

      if (devices && devices.length > 0) {
        return { error: 'لا يمكن حذف العميل لأنه يحتوي على أجهزة مسجلة' }
      }

      const { data: serviceRequests } = await supabase
        .from('service_requests')
        .select('id')
        .eq('customer_id', id)
        .limit(1)

      if (serviceRequests && serviceRequests.length > 0) {
        return { error: 'لا يمكن حذف العميل لأنه يحتوي على طلبات صيانة' }
      }

      const { error } = await supabase
        .from('customers')
        .delete()
        .eq('id', id)

      if (error) {
        console.error('Error deleting customer:', error)
        return { error: 'فشل في حذف العميل' }
      }

      return { message: 'تم حذف العميل بنجاح' }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Search customers
  static async search(query: string): Promise<ApiResponse<Customer[]>> {
    try {
      const { data, error } = await supabase
        .from('customers')
        .select('*')
        .or(`full_name.ilike.%${query}%,primary_phone.like.%${query}%,email.ilike.%${query}%,detailed_address.ilike.%${query}%`)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error searching customers:', error)
        return { error: 'فشل في البحث عن العملاء' }
      }

      return { data: data || [] }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Get customer statistics
  static async getStatistics(): Promise<ApiResponse<{
    total: number
    companies: number
    individuals: number
    recentlyAdded: number
  }>> {
    try {
      const { data: allCustomers, error } = await supabase
        .from('customers')
        .select('full_name, created_at')

      if (error) {
        console.error('Error fetching customer statistics:', error)
        return { error: 'فشل في جلب إحصائيات العملاء' }
      }

      const customers = allCustomers || []
      const companyKeywords = ['شركة', 'مؤسسة', 'مجموعة', 'شراكة', 'تجارة', 'خدمات']
      
      const companies = customers.filter(customer =>
        companyKeywords.some(keyword => customer.full_name.includes(keyword))
      ).length

      const individuals = customers.length - companies

      // Count customers added in the last 30 days
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
      
      const recentlyAdded = customers.filter(customer =>
        new Date(customer.created_at) > thirtyDaysAgo
      ).length

      return {
        data: {
          total: customers.length,
          companies,
          individuals,
          recentlyAdded
        }
      }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Get customers with their device count
  static async getWithDeviceCount(): Promise<ApiResponse<(Customer & { device_count: number })[]>> {
    try {
      const { data, error } = await supabase
        .from('customers')
        .select(`
          *,
          devices(count)
        `)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching customers with device count:', error)
        return { error: 'فشل في جلب بيانات العملاء' }
      }

      const customersWithCount = (data || []).map(customer => ({
        ...customer,
        device_count: customer.devices?.[0]?.count || 0
      }))

      return { data: customersWithCount }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }
}
