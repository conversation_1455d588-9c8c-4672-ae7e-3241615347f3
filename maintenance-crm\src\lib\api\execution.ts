import { supabase } from '@/lib/supabase'
import { Execution, ExecutionPart, ApiResponse } from '@/types'

interface ExecutionFormData {
  service_request_id: string
  engineer_id: string
  arrival_time?: string
  start_time?: string
  end_time?: string
  technical_description: string
  repair_actions: string
  failure_cause?: string
  tools_used?: string
  future_recommendations?: string
  received_by_customer?: string
  device_status_after: string
  customer_signature?: string
  technical_report?: string
}

export class ExecutionAPI {
  // Get all executions with related data
  static async getAll(): Promise<ApiResponse<Execution[]>> {
    try {
      const { data, error } = await supabase
        .from('execution')
        .select(`
          *,
          engineers(id, full_name, phone, specialization),
          service_requests(
            id, 
            initial_description,
            customers(id, full_name, primary_phone),
            devices(id, device_name, model, serial_number)
          )
        `)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching executions:', error)
        return { error: 'فشل في جلب تقارير التنفيذ' }
      }

      return { data: data || [] }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Get execution by ID
  static async getById(id: string): Promise<ApiResponse<Execution>> {
    try {
      const { data, error } = await supabase
        .from('execution')
        .select(`
          *,
          engineers(id, full_name, phone, email, specialization),
          service_requests(
            id, 
            initial_description, 
            priority,
            customers(id, full_name, primary_phone, detailed_address),
            devices(id, device_name, model, serial_number)
          )
        `)
        .eq('id', id)
        .single()

      if (error) {
        console.error('Error fetching execution:', error)
        return { error: 'فشل في جلب تقرير التنفيذ' }
      }

      if (!data) {
        return { error: 'تقرير التنفيذ غير موجود' }
      }

      return { data }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Get executions by engineer ID
  static async getByEngineerId(engineerId: string): Promise<ApiResponse<Execution[]>> {
    try {
      const { data, error } = await supabase
        .from('execution')
        .select(`
          *,
          service_requests(
            id, 
            initial_description,
            customers(id, full_name, primary_phone),
            devices(id, device_name, model)
          )
        `)
        .eq('engineer_id', engineerId)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching engineer executions:', error)
        return { error: 'فشل في جلب تقارير تنفيذ المهندس' }
      }

      return { data: data || [] }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Create new execution
  static async create(executionData: ExecutionFormData, usedParts: ExecutionPart[] = []): Promise<ApiResponse<Execution>> {
    try {
      // Verify service request exists
      const { data: serviceRequest } = await supabase
        .from('service_requests')
        .select('id, status')
        .eq('id', executionData.service_request_id)
        .single()

      if (!serviceRequest) {
        return { error: 'طلب الصيانة غير موجود' }
      }

      // Check if execution already exists for this service request
      const { data: existingExecution } = await supabase
        .from('execution')
        .select('id')
        .eq('service_request_id', executionData.service_request_id)
        .single()

      if (existingExecution) {
        return { error: 'يوجد تقرير تنفيذ مسبق لهذا الطلب' }
      }

      // Verify engineer exists
      const { data: engineer } = await supabase
        .from('engineers')
        .select('id, is_active')
        .eq('id', executionData.engineer_id)
        .single()

      if (!engineer) {
        return { error: 'المهندس غير موجود' }
      }

      if (!engineer.is_active) {
        return { error: 'المهندس غير نشط' }
      }

      // Create execution
      const { data: execution, error: executionError } = await supabase
        .from('execution')
        .insert([executionData])
        .select(`
          *,
          engineers(id, full_name, phone, specialization),
          service_requests(
            id, 
            initial_description,
            customers(id, full_name, primary_phone),
            devices(id, device_name, model, serial_number)
          )
        `)
        .single()

      if (executionError) {
        console.error('Error creating execution:', executionError)
        return { error: 'فشل في إنشاء تقرير التنفيذ' }
      }

      // Add used parts if any
      if (usedParts.length > 0) {
        const partsToInsert = usedParts.map(part => ({
          execution_id: execution.id,
          part_id: part.part_id,
          quantity_used: part.quantity_used,
          unit_price_at_time: part.unit_price_at_time
        }))

        const { error: partsError } = await supabase
          .from('execution_parts')
          .insert(partsToInsert)

        if (partsError) {
          console.error('Error adding execution parts:', partsError)
          // Don't fail the whole operation, just log the error
        } else {
          // Update parts stock
          for (const part of usedParts) {
            await supabase
              .from('parts')
              .update({ 
                stock_quantity: supabase.sql`stock_quantity - ${part.quantity_used}` 
              })
              .eq('id', part.part_id)
          }
        }
      }

      // Update service request status
      await supabase
        .from('service_requests')
        .update({ status: 'مكتمل' })
        .eq('id', executionData.service_request_id)

      // Update scheduling status if exists
      await supabase
        .from('scheduling')
        .update({ status: 'مكتمل' })
        .eq('service_request_id', executionData.service_request_id)

      return { 
        data: execution, 
        message: 'تم إنشاء تقرير التنفيذ بنجاح' 
      }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Update execution
  static async update(id: string, executionData: Partial<ExecutionFormData>, usedParts: ExecutionPart[] = []): Promise<ApiResponse<Execution>> {
    try {
      const { data: execution, error: executionError } = await supabase
        .from('execution')
        .update(executionData)
        .eq('id', id)
        .select(`
          *,
          engineers(id, full_name, phone, specialization),
          service_requests(
            id, 
            initial_description,
            customers(id, full_name, primary_phone),
            devices(id, device_name, model, serial_number)
          )
        `)
        .single()

      if (executionError) {
        console.error('Error updating execution:', executionError)
        return { error: 'فشل في تحديث تقرير التنفيذ' }
      }

      // Update used parts if provided
      if (usedParts.length >= 0) {
        // First, restore stock for existing parts
        const { data: existingParts } = await supabase
          .from('execution_parts')
          .select('part_id, quantity_used')
          .eq('execution_id', id)

        if (existingParts) {
          for (const existingPart of existingParts) {
            await supabase
              .from('parts')
              .update({ 
                stock_quantity: supabase.sql`stock_quantity + ${existingPart.quantity_used}` 
              })
              .eq('id', existingPart.part_id)
          }
        }

        // Delete existing parts
        await supabase
          .from('execution_parts')
          .delete()
          .eq('execution_id', id)

        // Add new parts
        if (usedParts.length > 0) {
          const partsToInsert = usedParts.map(part => ({
            execution_id: id,
            part_id: part.part_id,
            quantity_used: part.quantity_used,
            unit_price_at_time: part.unit_price_at_time
          }))

          const { error: partsError } = await supabase
            .from('execution_parts')
            .insert(partsToInsert)

          if (partsError) {
            console.error('Error updating execution parts:', partsError)
          } else {
            // Update parts stock
            for (const part of usedParts) {
              await supabase
                .from('parts')
                .update({ 
                  stock_quantity: supabase.sql`stock_quantity - ${part.quantity_used}` 
                })
                .eq('id', part.part_id)
            }
          }
        }
      }

      return { 
        data: execution, 
        message: 'تم تحديث تقرير التنفيذ بنجاح' 
      }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Delete execution
  static async delete(id: string): Promise<ApiResponse<void>> {
    try {
      // Get execution details before deletion
      const executionResult = await this.getById(id)
      if (executionResult.error || !executionResult.data) {
        return { error: 'تقرير التنفيذ غير موجود' }
      }

      // Restore parts stock
      const { data: usedParts } = await supabase
        .from('execution_parts')
        .select('part_id, quantity_used')
        .eq('execution_id', id)

      if (usedParts) {
        for (const part of usedParts) {
          await supabase
            .from('parts')
            .update({ 
              stock_quantity: supabase.sql`stock_quantity + ${part.quantity_used}` 
            })
            .eq('id', part.part_id)
        }
      }

      // Delete execution parts first
      await supabase
        .from('execution_parts')
        .delete()
        .eq('execution_id', id)

      // Delete execution
      const { error } = await supabase
        .from('execution')
        .delete()
        .eq('id', id)

      if (error) {
        console.error('Error deleting execution:', error)
        return { error: 'فشل في حذف تقرير التنفيذ' }
      }

      // Update service request status back to in progress
      if (executionResult.data.service_request_id) {
        await supabase
          .from('service_requests')
          .update({ status: 'قيد التنفيذ' })
          .eq('id', executionResult.data.service_request_id)
      }

      return { message: 'تم حذف تقرير التنفيذ بنجاح' }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Get execution with used parts
  static async getWithParts(id: string): Promise<ApiResponse<Execution & { execution_parts: ExecutionPart[] }>> {
    try {
      const executionResult = await this.getById(id)
      if (executionResult.error || !executionResult.data) {
        return executionResult as any
      }

      const { data: parts, error: partsError } = await supabase
        .from('execution_parts')
        .select(`
          *,
          parts(id, part_name, part_number, unit_price)
        `)
        .eq('execution_id', id)

      if (partsError) {
        console.error('Error fetching execution parts:', partsError)
        return { error: 'فشل في جلب قطع الغيار المستخدمة' }
      }

      return {
        data: {
          ...executionResult.data,
          execution_parts: parts || []
        }
      }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Get execution statistics
  static async getStatistics(): Promise<ApiResponse<{
    total: number
    completed: number
    inProgress: number
    notStarted: number
    averageDuration: number
  }>> {
    try {
      const { data: allExecutions, error } = await supabase
        .from('execution')
        .select('start_time, end_time')

      if (error) {
        console.error('Error fetching execution statistics:', error)
        return { error: 'فشل في جلب إحصائيات التنفيذ' }
      }

      const executions = allExecutions || []
      
      const completed = executions.filter(e => e.end_time).length
      const inProgress = executions.filter(e => e.start_time && !e.end_time).length
      const notStarted = executions.filter(e => !e.start_time).length

      // Calculate average duration for completed executions
      const completedExecutions = executions.filter(e => e.start_time && e.end_time)
      let averageDuration = 0
      
      if (completedExecutions.length > 0) {
        const totalDuration = completedExecutions.reduce((sum, execution) => {
          const start = new Date(execution.start_time!)
          const end = new Date(execution.end_time!)
          return sum + (end.getTime() - start.getTime())
        }, 0)
        
        averageDuration = totalDuration / completedExecutions.length / (1000 * 60 * 60) // Convert to hours
      }

      return {
        data: {
          total: executions.length,
          completed,
          inProgress,
          notStarted,
          averageDuration: Math.round(averageDuration * 100) / 100
        }
      }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }
}
