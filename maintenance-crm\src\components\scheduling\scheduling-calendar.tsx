'use client'

import { useState } from 'react'
import { Scheduling } from '@/types'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  ChevronLeft, 
  ChevronRight, 
  Calendar,
  Clock,
  User,
  Plus
} from 'lucide-react'
import { formatTime } from '@/lib/utils'

interface SchedulingCalendarProps {
  schedulings: Scheduling[]
  onSchedulingClick: (scheduling: Scheduling) => void
  onAddScheduling: (date: Date) => void
}

const DAYS_OF_WEEK = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت']
const MONTHS = [
  'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
  'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
]

export function SchedulingCalendar({ 
  schedulings, 
  onSchedulingClick, 
  onAddScheduling 
}: SchedulingCalendarProps) {
  const [currentDate, setCurrentDate] = useState(new Date())

  const getStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      'مجدول': 'bg-blue-100 text-blue-800 border-blue-200',
      'مؤكد': 'bg-green-100 text-green-800 border-green-200',
      'في الطريق': 'bg-yellow-100 text-yellow-800 border-yellow-200',
      'وصل': 'bg-purple-100 text-purple-800 border-purple-200',
      'مكتمل': 'bg-green-100 text-green-800 border-green-200',
      'ملغي': 'bg-red-100 text-red-800 border-red-200',
    }
    return colors[status] || 'bg-gray-100 text-gray-800 border-gray-200'
  }

  const getPriorityColor = (priority: string) => {
    const colors: Record<string, string> = {
      'عاجل': 'border-r-4 border-r-red-500',
      'متوسط': 'border-r-4 border-r-yellow-500',
      'منخفض': 'border-r-4 border-r-green-500',
    }
    return colors[priority] || ''
  }

  // Get first day of the month
  const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1)
  const lastDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0)
  
  // Get the day of week for the first day (0 = Sunday)
  const startingDayOfWeek = firstDayOfMonth.getDay()
  
  // Get number of days in the month
  const daysInMonth = lastDayOfMonth.getDate()
  
  // Get previous month's last few days to fill the calendar
  const daysFromPrevMonth = startingDayOfWeek
  const prevMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 0)
  const daysInPrevMonth = prevMonth.getDate()

  // Generate calendar days
  const calendarDays = []
  
  // Previous month days
  for (let i = daysFromPrevMonth - 1; i >= 0; i--) {
    calendarDays.push({
      date: new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, daysInPrevMonth - i),
      isCurrentMonth: false
    })
  }
  
  // Current month days
  for (let day = 1; day <= daysInMonth; day++) {
    calendarDays.push({
      date: new Date(currentDate.getFullYear(), currentDate.getMonth(), day),
      isCurrentMonth: true
    })
  }
  
  // Next month days to complete the grid (42 days = 6 weeks)
  const remainingDays = 42 - calendarDays.length
  for (let day = 1; day <= remainingDays; day++) {
    calendarDays.push({
      date: new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, day),
      isCurrentMonth: false
    })
  }

  // Get schedulings for a specific date
  const getSchedulingsForDate = (date: Date) => {
    const dateString = date.toDateString()
    return schedulings.filter(scheduling => 
      new Date(scheduling.scheduled_date).toDateString() === dateString
    )
  }

  // Navigate to previous month
  const goToPreviousMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1))
  }

  // Navigate to next month
  const goToNextMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1))
  }

  // Go to today
  const goToToday = () => {
    setCurrentDate(new Date())
  }

  const isToday = (date: Date) => {
    const today = new Date()
    return date.toDateString() === today.toDateString()
  }

  const isPast = (date: Date) => {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    return date < today
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-6 w-6 text-blue-600" />
            تقويم الجدولة
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={goToToday}>
              اليوم
            </Button>
            <Button variant="outline" size="sm" onClick={goToPreviousMonth}>
              <ChevronRight className="h-4 w-4" />
            </Button>
            <span className="text-lg font-semibold min-w-[120px] text-center">
              {MONTHS[currentDate.getMonth()]} {currentDate.getFullYear()}
            </span>
            <Button variant="outline" size="sm" onClick={goToNextMonth}>
              <ChevronLeft className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Days of week header */}
        <div className="grid grid-cols-7 gap-1 mb-2">
          {DAYS_OF_WEEK.map((day) => (
            <div key={day} className="p-2 text-center text-sm font-medium text-gray-500">
              {day}
            </div>
          ))}
        </div>

        {/* Calendar grid */}
        <div className="grid grid-cols-7 gap-1">
          {calendarDays.map((calendarDay, index) => {
            const daySchedulings = getSchedulingsForDate(calendarDay.date)
            const isCurrentMonthDay = calendarDay.isCurrentMonth
            const isTodayDate = isToday(calendarDay.date)
            const isPastDate = isPast(calendarDay.date)

            return (
              <div
                key={index}
                className={`
                  min-h-[120px] p-1 border border-gray-200 relative
                  ${isCurrentMonthDay ? 'bg-white' : 'bg-gray-50'}
                  ${isTodayDate ? 'bg-blue-50 border-blue-300' : ''}
                  ${isPastDate && isCurrentMonthDay ? 'bg-gray-50' : ''}
                `}
              >
                {/* Date number */}
                <div className="flex items-center justify-between mb-1">
                  <span className={`
                    text-sm font-medium
                    ${isCurrentMonthDay ? 'text-gray-900' : 'text-gray-400'}
                    ${isTodayDate ? 'text-blue-600 font-bold' : ''}
                  `}>
                    {calendarDay.date.getDate()}
                  </span>
                  
                  {/* Add scheduling button */}
                  {isCurrentMonthDay && !isPastDate && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0 opacity-0 hover:opacity-100 transition-opacity"
                      onClick={() => onAddScheduling(calendarDay.date)}
                    >
                      <Plus className="h-3 w-3" />
                    </Button>
                  )}
                </div>

                {/* Schedulings for this day */}
                <div className="space-y-1">
                  {daySchedulings.slice(0, 3).map((scheduling) => (
                    <div
                      key={scheduling.id}
                      className={`
                        text-xs p-1 rounded cursor-pointer hover:shadow-sm transition-shadow
                        ${getStatusColor(scheduling.status)}
                        ${getPriorityColor(scheduling.service_request?.priority || '')}
                      `}
                      onClick={() => onSchedulingClick(scheduling)}
                    >
                      <div className="flex items-center gap-1 mb-1">
                        <Clock className="h-3 w-3" />
                        <span className="font-medium">
                          {formatTime(scheduling.scheduled_date)}
                        </span>
                      </div>
                      <div className="flex items-center gap-1 mb-1">
                        <User className="h-3 w-3" />
                        <span className="truncate">
                          {scheduling.engineer?.full_name}
                        </span>
                      </div>
                      <div className="truncate text-gray-600">
                        {scheduling.service_request?.customer?.full_name}
                      </div>
                    </div>
                  ))}
                  
                  {/* Show more indicator */}
                  {daySchedulings.length > 3 && (
                    <div className="text-xs text-gray-500 text-center py-1">
                      +{daySchedulings.length - 3} أخرى
                    </div>
                  )}
                </div>
              </div>
            )
          })}
        </div>

        {/* Legend */}
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex flex-wrap gap-4 text-xs">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-blue-100 border border-blue-200 rounded"></div>
              <span>مجدول</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-100 border border-green-200 rounded"></div>
              <span>مؤكد</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-yellow-100 border border-yellow-200 rounded"></div>
              <span>في الطريق</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-purple-100 border border-purple-200 rounded"></div>
              <span>وصل</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-red-100 border border-red-200 rounded"></div>
              <span>ملغي</span>
            </div>
          </div>
          <div className="flex flex-wrap gap-4 text-xs mt-2">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 border-r-4 border-r-red-500 bg-gray-100"></div>
              <span>عاجل</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 border-r-4 border-r-yellow-500 bg-gray-100"></div>
              <span>متوسط</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 border-r-4 border-r-green-500 bg-gray-100"></div>
              <span>منخفض</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
