-- Insert sample engineers
INSERT INTO engineers (full_name, phone, email, specialization, is_active) VALUES
('أحمد محمد العلي', '+966501234567', '<EMAIL>', 'أجهزة التكييف', true),
('فاطمة سعد الأحمد', '+966502345678', '<EMAIL>', 'الأجهزة الكهربائية', true),
('محمد عبدالله الخالد', '+966503456789', '<EMAIL>', 'أجهزة التبريد', true),
('نورا علي السالم', '+966504567890', '<EMAIL>', 'الأجهزة المنزلية', true),
('خالد أحمد المطيري', '+966505678901', '<EMAIL>', 'أجهزة التكييف', true);

-- Insert sample customers
INSERT INTO customers (full_name, primary_phone, secondary_phone, email, detailed_address, landmark, contact_person, special_notes) VALUES
('شركة الرياض للتجارة', '+966112345678', '+966112345679', '<EMAIL>', 'شارع الملك فهد، حي العليا، الرياض', 'بجانب برج المملكة', 'أحمد المدير العام', 'يفضل الاتصال صباحاً'),
('مؤسسة النور للخدمات', '+966113456789', null, '<EMAIL>', 'طريق الملك عبدالعزيز، حي الملز، الرياض', 'مقابل مستشفى الملك فيصل', 'فاطمة مديرة العمليات', null),
('عبدالرحمن محمد الغامدي', '+966501111111', '+966502222222', '<EMAIL>', 'حي النرجس، شارع التخصصي، الرياض', 'فيلا رقم 123', null, 'لديه حساسية من الغبار'),
('شركة الخليج للمقاولات', '+966114567890', '+966114567891', '<EMAIL>', 'شارع العروبة، حي الورود، الرياض', 'بجانب مجمع الراشد', 'سعد مدير المشاريع', 'يفضل المواعيد بعد الظهر'),
('منى سالم الدوسري', '+966503333333', null, '<EMAIL>', 'حي الياسمين، شارع الأمير سلطان، الرياض', 'عمارة الياسمين، الدور الثالث', null, null);

-- Insert sample devices
INSERT INTO devices (customer_id, device_name, model, serial_number, purchase_date, warranty_end_date, original_invoice_number) VALUES
((SELECT id FROM customers WHERE full_name = 'شركة الرياض للتجارة'), 'مكيف سبليت', 'Samsung AR24', 'SAM2024001', '2024-01-15', '2026-01-15', 'INV-2024-001'),
((SELECT id FROM customers WHERE full_name = 'شركة الرياض للتجارة'), 'ثلاجة تجارية', 'LG GR-X247', 'LG2024002', '2024-02-20', '2026-02-20', 'INV-2024-002'),
((SELECT id FROM customers WHERE full_name = 'مؤسسة النور للخدمات'), 'مكيف مركزي', 'Carrier 30RB', 'CAR2024003', '2024-03-10', '2027-03-10', 'INV-2024-003'),
((SELECT id FROM customers WHERE full_name = 'عبدالرحمن محمد الغامدي'), 'غسالة ملابس', 'Bosch WAT28', 'BOS2024004', '2024-04-05', '2026-04-05', 'INV-2024-004'),
((SELECT id FROM customers WHERE full_name = 'شركة الخليج للمقاولات'), 'مولد كهرباء', 'Caterpillar C15', 'CAT2024005', '2024-05-12', '2026-05-12', 'INV-2024-005'),
((SELECT id FROM customers WHERE full_name = 'منى سالم الدوسري'), 'مكيف شباك', 'General Electric GE12', 'GE2024006', '2024-06-18', '2025-06-18', 'INV-2024-006');

-- Insert sample parts
INSERT INTO parts (part_name, part_number, description, unit_price, stock_quantity) VALUES
('فلتر هواء مكيف', 'FILTER-AC-001', 'فلتر هواء قابل للغسل للمكيفات السبليت', 45.00, 50),
('غاز تبريد R410A', 'GAS-R410A-1KG', 'غاز تبريد صديق للبيئة - 1 كيلو', 120.00, 25),
('ضاغط مكيف 1.5 حصان', 'COMP-15HP-001', 'ضاغط مكيف قدرة 1.5 حصان', 850.00, 10),
('مروحة تبريد', 'FAN-COOL-001', 'مروحة تبريد للثلاجات التجارية', 180.00, 30),
('ترموستات رقمي', 'THERMO-DIG-001', 'ترموستات رقمي قابل للبرمجة', 95.00, 40),
('سير مولد', 'BELT-GEN-001', 'سير مولد مقاوم للحرارة', 65.00, 20),
('مضخة مياه', 'PUMP-WATER-001', 'مضخة مياه للغسالات', 220.00, 15),
('لوحة تحكم إلكترونية', 'PCB-CTRL-001', 'لوحة تحكم إلكترونية للأجهزة المنزلية', 320.00, 12);

-- Insert sample service requests
INSERT INTO service_requests (customer_id, device_id, received_by, request_source, initial_description, priority, status) VALUES
(
    (SELECT id FROM customers WHERE full_name = 'شركة الرياض للتجارة'),
    (SELECT id FROM devices WHERE serial_number = 'SAM2024001'),
    'سارة أحمد - خدمة العملاء',
    'هاتف',
    'المكيف لا يبرد بشكل جيد ويصدر أصوات غريبة',
    'عاجل',
    'تم تحديد موعد'
),
(
    (SELECT id FROM customers WHERE full_name = 'مؤسسة النور للخدمات'),
    (SELECT id FROM devices WHERE serial_number = 'CAR2024003'),
    'محمد علي - خدمة العملاء',
    'بريد إلكتروني',
    'المكيف المركزي يتوقف عن العمل بشكل متكرر',
    'عاجل',
    'قيد التنفيذ'
),
(
    (SELECT id FROM customers WHERE full_name = 'عبدالرحمن محمد الغامدي'),
    (SELECT id FROM devices WHERE serial_number = 'BOS2024004'),
    'فاطمة سعد - خدمة العملاء',
    'زيارة',
    'الغسالة لا تعصر الملابس بشكل جيد',
    'متوسط',
    'تم تسجيله'
),
(
    (SELECT id FROM customers WHERE full_name = 'شركة الخليج للمقاولات'),
    (SELECT id FROM devices WHERE serial_number = 'CAT2024005'),
    'أحمد محمد - خدمة العملاء',
    'هاتف',
    'المولد لا يعمل عند الحاجة إليه',
    'عاجل',
    'في انتظار فني'
),
(
    (SELECT id FROM customers WHERE full_name = 'منى سالم الدوسري'),
    (SELECT id FROM devices WHERE serial_number = 'GE2024006'),
    'نورا علي - خدمة العملاء',
    'نموذج إلكتروني',
    'المكيف يسرب مياه على الأرض',
    'منخفض',
    'قيد المراجعة'
);

-- Insert sample scheduling
INSERT INTO scheduling (service_request_id, engineer_id, scheduled_date, estimated_duration, engineer_notes, status) VALUES
(
    (SELECT id FROM service_requests WHERE initial_description LIKE '%المكيف لا يبرد%'),
    (SELECT id FROM engineers WHERE full_name = 'أحمد محمد العلي'),
    '2025-01-15 10:00:00+03',
    120,
    'يحتاج فحص الضاغط والفلاتر',
    'مؤكد'
),
(
    (SELECT id FROM service_requests WHERE initial_description LIKE '%المكيف المركزي%'),
    (SELECT id FROM engineers WHERE full_name = 'محمد عبدالله الخالد'),
    '2025-01-14 14:00:00+03',
    180,
    'فحص شامل للنظام المركزي',
    'في الطريق'
);

-- Insert sample execution
INSERT INTO execution (service_request_id, engineer_id, arrival_time, start_time, end_time, technical_description, repair_actions, failure_cause, tools_used, future_recommendations, received_by_customer, device_status_after) VALUES
(
    (SELECT id FROM service_requests WHERE initial_description LIKE '%المكيف المركزي%'),
    (SELECT id FROM engineers WHERE full_name = 'محمد عبدالله الخالد'),
    '2025-01-14 14:15:00+03',
    '2025-01-14 14:30:00+03',
    '2025-01-14 17:00:00+03',
    'تم فحص النظام المركزي ووجد انسداد في المبخر وتسريب في غاز التبريد',
    'تنظيف المبخر، إصلاح التسريب، إعادة تعبئة غاز التبريد، اختبار النظام',
    'عدم الصيانة الدورية أدى إلى تراكم الأتربة وتآكل الأنابيب',
    'أدوات تنظيف، جهاز كشف التسريب، مضخة تفريغ، غاز تبريد',
    'ضرورة تنظيف الفلاتر شهرياً وصيانة دورية كل 6 أشهر',
    'فاطمة مديرة العمليات',
    'يعمل بكفاءة عالية'
);

-- Insert sample execution parts
INSERT INTO execution_parts (execution_id, part_id, quantity_used, unit_price_at_time) VALUES
(
    (SELECT id FROM execution WHERE technical_description LIKE '%تم فحص النظام المركزي%'),
    (SELECT id FROM parts WHERE part_name = 'غاز تبريد R410A'),
    2,
    120.00
),
(
    (SELECT id FROM execution WHERE technical_description LIKE '%تم فحص النظام المركزي%'),
    (SELECT id FROM parts WHERE part_name = 'فلتر هواء مكيف'),
    1,
    45.00
);

-- Insert sample follow-up
INSERT INTO follow_up (service_request_id, invoice_number, payment_status, closure_date, customer_service_notes, customer_rating, customer_feedback, engineer_performance_rating, internal_notes) VALUES
(
    (SELECT id FROM service_requests WHERE initial_description LIKE '%المكيف المركزي%'),
    'INV-MAINT-2025-001',
    'تم الدفع',
    '2025-01-15 10:00:00+03',
    'العميل راضي جداً عن الخدمة وسرعة الاستجابة',
    5,
    'خدمة ممتازة والمهندس محترف جداً',
    5,
    'عميل مميز يستحق المتابعة الدورية'
);

-- Insert sample invoices
INSERT INTO invoices (service_request_id, invoice_number, service_cost, parts_cost, payment_status, payment_date) VALUES
(
    (SELECT id FROM service_requests WHERE initial_description LIKE '%المكيف المركزي%'),
    'INV-MAINT-2025-001',
    300.00,
    285.00,
    'مدفوع بالكامل',
    '2025-01-15 10:00:00+03'
);
