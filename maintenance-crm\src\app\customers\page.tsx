'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, Devi<PERSON>, ServiceRequest } from '@/types'
import { CustomerList } from '@/components/customers/customer-list'
import { CustomerForm } from '@/components/customers/customer-form'
import { CustomerDetails } from '@/components/customers/customer-details'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ArrowRight, Users } from 'lucide-react'
import { CustomersAPI } from '@/lib/api/customers'
import { DevicesAPI } from '@/lib/api/devices'
import Link from 'next/link'

type ViewMode = 'list' | 'add' | 'edit' | 'view'

export default function CustomersPage() {
  const [customers, setCustomers] = useState<Customer[]>([])
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null)
  const [customerDevices, setCustomerDevices] = useState<Device[]>([])
  const [customerRequests, setCustomerRequests] = useState<ServiceRequest[]>([])
  const [viewMode, setViewMode] = useState<ViewMode>('list')
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Load customers data
  useEffect(() => {
    loadCustomers()
  }, [])

  const loadCustomers = async () => {
    setIsLoading(true)
    setError(null)

    const result = await CustomersAPI.getAll()

    if (result.error) {
      setError(result.error)
      // Fallback to mock data for development
      const mockCustomers: Customer[] = [
        {
          id: '1',
          full_name: 'شركة الرياض للتجارة',
          primary_phone: '+966112345678',
          secondary_phone: '+966112345679',
          email: '<EMAIL>',
          detailed_address: 'شارع الملك فهد، حي العليا، الرياض',
          landmark: 'بجانب برج المملكة',
          contact_person: 'أحمد المدير العام',
          special_notes: 'يفضل الاتصال صباحاً',
          created_at: '2024-01-15T10:00:00Z',
          updated_at: '2024-01-15T10:00:00Z'
        },
        {
          id: '2',
          full_name: 'عبدالرحمن محمد الغامدي',
          primary_phone: '+966501111111',
          secondary_phone: '+966502222222',
          email: '<EMAIL>',
          detailed_address: 'حي النرجس، شارع التخصصي، الرياض',
          landmark: 'فيلا رقم 123',
          contact_person: undefined,
          special_notes: 'لديه حساسية من الغبار',
          created_at: '2024-02-10T14:30:00Z',
          updated_at: '2024-02-10T14:30:00Z'
        }
      ]
      setCustomers(mockCustomers)
    } else {
      setCustomers(result.data || [])
    }

    setIsLoading(false)
  }

  const loadCustomerDetails = async (customerId: string) => {
    // Load customer devices
    const devicesResult = await DevicesAPI.getByCustomerId(customerId)
    if (devicesResult.data) {
      setCustomerDevices(devicesResult.data)
    }

    // Load customer service requests (will be implemented later)
    setCustomerRequests([])
  }

  const handleAddCustomer = async (data: any) => {
    const result = await CustomersAPI.create(data)

    if (result.error) {
      alert(`خطأ: ${result.error}`)
      return
    }

    if (result.data) {
      setCustomers(prev => [result.data!, ...prev])
      setViewMode('list')
      alert(result.message || 'تم إضافة العميل بنجاح!')
    }
  }

  const handleEditCustomer = async (data: any) => {
    if (!selectedCustomer) return

    const result = await CustomersAPI.update(selectedCustomer.id, data)

    if (result.error) {
      alert(`خطأ: ${result.error}`)
      return
    }

    if (result.data) {
      setCustomers(prev =>
        prev.map(customer =>
          customer.id === selectedCustomer.id ? result.data! : customer
        )
      )
      setSelectedCustomer(result.data)
      setViewMode('view')
      alert(result.message || 'تم تحديث بيانات العميل بنجاح!')
    }
  }

  const handleViewCustomer = async (customer: Customer) => {
    setSelectedCustomer(customer)
    await loadCustomerDetails(customer.id)
    setViewMode('view')
  }

  const handleEditMode = (customer: Customer) => {
    setSelectedCustomer(customer)
    setViewMode('edit')
  }

  const handleBackToList = () => {
    setSelectedCustomer(null)
    setViewMode('list')
  }

  const renderContent = () => {
    switch (viewMode) {
      case 'add':
        return (
          <CustomerForm
            onSubmit={handleAddCustomer}
            onCancel={handleBackToList}
          />
        )
      
      case 'edit':
        return selectedCustomer ? (
          <CustomerForm
            initialData={selectedCustomer}
            onSubmit={handleEditCustomer}
            onCancel={handleBackToList}
          />
        ) : null
      
      case 'view':
        return selectedCustomer ? (
          <CustomerDetails
            customer={selectedCustomer}
            devices={customerDevices}
            recentRequests={customerRequests}
            onEdit={() => setViewMode('edit')}
            onAddDevice={() => {
              // سيتم تنفيذ هذا لاحقاً
              alert('سيتم إضافة هذه الميزة قريباً')
            }}
            onAddServiceRequest={() => {
              // سيتم تنفيذ هذا لاحقاً
              alert('سيتم إضافة هذه الميزة قريباً')
            }}
          />
        ) : null
      
      default:
        return (
          <CustomerList
            customers={customers}
            onEdit={handleEditMode}
            onView={handleViewCustomer}
            onAdd={() => setViewMode('add')}
            isLoading={isLoading}
          />
        )
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center gap-4">
              {viewMode !== 'list' && (
                <Button
                  variant="ghost"
                  onClick={handleBackToList}
                  className="flex items-center gap-2"
                >
                  <ArrowRight className="h-4 w-4" />
                  العودة للقائمة
                </Button>
              )}
              <div className="flex items-center gap-3">
                <Users className="h-8 w-8 text-blue-600" />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    {viewMode === 'add' && 'إضافة عميل جديد'}
                    {viewMode === 'edit' && 'تعديل بيانات العميل'}
                    {viewMode === 'view' && 'تفاصيل العميل'}
                    {viewMode === 'list' && 'إدارة العملاء'}
                  </h1>
                  <p className="text-gray-600">
                    {viewMode === 'add' && 'أدخل بيانات العميل الجديد'}
                    {viewMode === 'edit' && 'قم بتعديل بيانات العميل'}
                    {viewMode === 'view' && selectedCustomer?.full_name}
                    {viewMode === 'list' && 'عرض وإدارة جميع العملاء في النظام'}
                  </p>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <Link href="/">
                <Button variant="outline">
                  العودة للرئيسية
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Statistics Cards - only show in list view */}
        {viewMode === 'list' && !isLoading && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">إجمالي العملاء</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{customers.length}</div>
                <p className="text-xs text-muted-foreground">
                  عميل مسجل في النظام
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">المؤسسات</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {customers.filter(c => 
                    ['شركة', 'مؤسسة', 'مجموعة'].some(keyword => 
                      c.full_name.includes(keyword)
                    )
                  ).length}
                </div>
                <p className="text-xs text-muted-foreground">
                  مؤسسة وشركة
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">الأفراد</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {customers.filter(c => 
                    !['شركة', 'مؤسسة', 'مجموعة'].some(keyword => 
                      c.full_name.includes(keyword)
                    )
                  ).length}
                </div>
                <p className="text-xs text-muted-foreground">
                  عميل فردي
                </p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Main Content Area */}
        {renderContent()}
      </div>
    </div>
  )
}
