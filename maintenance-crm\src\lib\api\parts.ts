import { supabase } from '@/lib/supabase'
import { Part, ApiResponse } from '@/types'

interface PartFormData {
  part_name: string
  part_number: string
  description?: string
  unit_price: number
  stock_quantity: number
}

export class PartsAPI {
  // Get all parts
  static async getAll(): Promise<ApiResponse<Part[]>> {
    try {
      const { data, error } = await supabase
        .from('parts')
        .select('*')
        .order('part_name', { ascending: true })

      if (error) {
        console.error('Error fetching parts:', error)
        return { error: 'فشل في جلب قطع الغيار' }
      }

      return { data: data || [] }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Get part by ID
  static async getById(id: string): Promise<ApiResponse<Part>> {
    try {
      const { data, error } = await supabase
        .from('parts')
        .select('*')
        .eq('id', id)
        .single()

      if (error) {
        console.error('Error fetching part:', error)
        return { error: 'فشل في جلب قطعة الغيار' }
      }

      if (!data) {
        return { error: 'قطعة الغيار غير موجودة' }
      }

      return { data }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Create new part
  static async create(partData: PartFormData): Promise<ApiResponse<Part>> {
    try {
      // Check if part number already exists
      const { data: existingPart } = await supabase
        .from('parts')
        .select('id')
        .eq('part_number', partData.part_number)
        .single()

      if (existingPart) {
        return { error: 'رقم القطعة مسجل مسبقاً' }
      }

      const { data, error } = await supabase
        .from('parts')
        .insert([partData])
        .select()
        .single()

      if (error) {
        console.error('Error creating part:', error)
        return { error: 'فشل في إنشاء قطعة الغيار' }
      }

      return { 
        data, 
        message: 'تم إنشاء قطعة الغيار بنجاح' 
      }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Update part
  static async update(id: string, partData: Partial<PartFormData>): Promise<ApiResponse<Part>> {
    try {
      // If updating part number, check if it's already used by another part
      if (partData.part_number) {
        const { data: existingPart } = await supabase
          .from('parts')
          .select('id')
          .eq('part_number', partData.part_number)
          .neq('id', id)
          .single()

        if (existingPart) {
          return { error: 'رقم القطعة مسجل مسبقاً لقطعة أخرى' }
        }
      }

      const { data, error } = await supabase
        .from('parts')
        .update(partData)
        .eq('id', id)
        .select()
        .single()

      if (error) {
        console.error('Error updating part:', error)
        return { error: 'فشل في تحديث قطعة الغيار' }
      }

      return { 
        data, 
        message: 'تم تحديث قطعة الغيار بنجاح' 
      }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Delete part
  static async delete(id: string): Promise<ApiResponse<void>> {
    try {
      // Check if part is used in any executions
      const { data: executionParts } = await supabase
        .from('execution_parts')
        .select('id')
        .eq('part_id', id)
        .limit(1)

      if (executionParts && executionParts.length > 0) {
        return { error: 'لا يمكن حذف قطعة الغيار لأنها مستخدمة في تقارير التنفيذ' }
      }

      const { error } = await supabase
        .from('parts')
        .delete()
        .eq('id', id)

      if (error) {
        console.error('Error deleting part:', error)
        return { error: 'فشل في حذف قطعة الغيار' }
      }

      return { message: 'تم حذف قطعة الغيار بنجاح' }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Search parts
  static async search(query: string): Promise<ApiResponse<Part[]>> {
    try {
      const { data, error } = await supabase
        .from('parts')
        .select('*')
        .or(`part_name.ilike.%${query}%,part_number.ilike.%${query}%,description.ilike.%${query}%`)
        .order('part_name', { ascending: true })

      if (error) {
        console.error('Error searching parts:', error)
        return { error: 'فشل في البحث عن قطع الغيار' }
      }

      return { data: data || [] }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Get low stock parts
  static async getLowStock(threshold: number = 10): Promise<ApiResponse<Part[]>> {
    try {
      const { data, error } = await supabase
        .from('parts')
        .select('*')
        .lte('stock_quantity', threshold)
        .order('stock_quantity', { ascending: true })

      if (error) {
        console.error('Error fetching low stock parts:', error)
        return { error: 'فشل في جلب قطع الغيار منخفضة المخزون' }
      }

      return { data: data || [] }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Get parts statistics
  static async getStatistics(): Promise<ApiResponse<{
    total: number
    lowStock: number
    outOfStock: number
    totalValue: number
    averagePrice: number
  }>> {
    try {
      const { data: allParts, error } = await supabase
        .from('parts')
        .select('unit_price, stock_quantity')

      if (error) {
        console.error('Error fetching parts statistics:', error)
        return { error: 'فشل في جلب إحصائيات قطع الغيار' }
      }

      const parts = allParts || []
      
      const lowStock = parts.filter(p => p.stock_quantity <= 10 && p.stock_quantity > 0).length
      const outOfStock = parts.filter(p => p.stock_quantity === 0).length
      
      const totalValue = parts.reduce((sum, part) => 
        sum + (part.unit_price * part.stock_quantity), 0
      )
      
      const averagePrice = parts.length > 0 
        ? parts.reduce((sum, part) => sum + part.unit_price, 0) / parts.length 
        : 0

      return {
        data: {
          total: parts.length,
          lowStock,
          outOfStock,
          totalValue: Math.round(totalValue * 100) / 100,
          averagePrice: Math.round(averagePrice * 100) / 100
        }
      }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Update stock quantity
  static async updateStock(id: string, newQuantity: number): Promise<ApiResponse<Part>> {
    try {
      if (newQuantity < 0) {
        return { error: 'كمية المخزون لا يمكن أن تكون سالبة' }
      }

      const { data, error } = await supabase
        .from('parts')
        .update({ stock_quantity: newQuantity })
        .eq('id', id)
        .select()
        .single()

      if (error) {
        console.error('Error updating stock:', error)
        return { error: 'فشل في تحديث كمية المخزون' }
      }

      return { 
        data, 
        message: 'تم تحديث كمية المخزون بنجاح' 
      }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Adjust stock (add or subtract)
  static async adjustStock(id: string, adjustment: number, reason?: string): Promise<ApiResponse<Part>> {
    try {
      const currentPart = await this.getById(id)
      if (currentPart.error || !currentPart.data) {
        return { error: 'قطعة الغيار غير موجودة' }
      }

      const newQuantity = currentPart.data.stock_quantity + adjustment
      
      if (newQuantity < 0) {
        return { error: 'الكمية المطلوبة أكبر من المتوفر في المخزون' }
      }

      const { data, error } = await supabase
        .from('parts')
        .update({ stock_quantity: newQuantity })
        .eq('id', id)
        .select()
        .single()

      if (error) {
        console.error('Error adjusting stock:', error)
        return { error: 'فشل في تعديل كمية المخزون' }
      }

      // TODO: Log stock movement in a separate table if needed
      // This would include: part_id, old_quantity, new_quantity, adjustment, reason, timestamp

      return { 
        data, 
        message: `تم ${adjustment > 0 ? 'إضافة' : 'خصم'} ${Math.abs(adjustment)} قطعة ${adjustment > 0 ? 'إلى' : 'من'} المخزون` 
      }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Get most used parts
  static async getMostUsed(limit: number = 10): Promise<ApiResponse<(Part & { usage_count: number })[]>> {
    try {
      const { data, error } = await supabase
        .from('parts')
        .select(`
          *,
          execution_parts(quantity_used)
        `)
        .order('part_name', { ascending: true })

      if (error) {
        console.error('Error fetching most used parts:', error)
        return { error: 'فشل في جلب قطع الغيار الأكثر استخداماً' }
      }

      const partsWithUsage = (data || []).map(part => {
        const usageCount = (part.execution_parts || []).reduce(
          (sum: number, ep: any) => sum + ep.quantity_used, 0
        )
        return {
          ...part,
          usage_count: usageCount,
          execution_parts: undefined // Remove from result
        }
      }).sort((a, b) => b.usage_count - a.usage_count)
        .slice(0, limit)

      return { data: partsWithUsage }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Get parts by price range
  static async getByPriceRange(minPrice: number, maxPrice: number): Promise<ApiResponse<Part[]>> {
    try {
      const { data, error } = await supabase
        .from('parts')
        .select('*')
        .gte('unit_price', minPrice)
        .lte('unit_price', maxPrice)
        .order('unit_price', { ascending: true })

      if (error) {
        console.error('Error fetching parts by price range:', error)
        return { error: 'فشل في جلب قطع الغيار حسب النطاق السعري' }
      }

      return { data: data || [] }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }
}
