# 🚀 تشغيل نظام إدارة علاقات العملاء للصيانة

## طرق التشغيل المختلفة

### 🖥️ Windows
```cmd
# الطريقة الأسهل
start.bat

# أو يدوياً
npm run setup
npm run dev
```

### 🍎 Mac / 🐧 Linux
```bash
# الطريقة الأسهل
./start.sh

# أو يدوياً
npm run setup
npm run dev
```

### 📱 جميع الأنظمة
```bash
# فحص النظام أولاً
npm run check

# تثبيت وتشغيل
npm run setup
npm run dev
```

---

## 🔧 أوامر مفيدة

| الأمر | الوصف |
|-------|--------|
| `npm run check` | فحص حالة النظام |
| `npm run setup` | تثبيت التبعيات وفحص النظام |
| `npm run dev` | تشغيل النظام للتطوير |
| `npm run build` | بناء النظام للإنتاج |
| `npm run start` | تشغيل النظام المبني |
| `npm run clean` | تنظيف وإعادة تثبيت |
| `npm run lint` | فحص جودة الكود |

---

## 📋 قائمة التحقق السريع

### ✅ قبل التشغيل
- [ ] Node.js 18+ مثبت
- [ ] ملف `.env.local` موجود ومعدّل
- [ ] قاعدة بيانات Supabase منشأة
- [ ] SQL scripts منفذة

### ✅ بعد التشغيل
- [ ] النظام يعمل على http://localhost:3000
- [ ] الصفحة الرئيسية تظهر بالعربية
- [ ] يمكن الوصول لجميع الصفحات
- [ ] لا توجد أخطاء في Console

---

## 🌐 الوصول للنظام

بعد التشغيل الناجح:

**الرابط الرئيسي:** http://localhost:3000

**الصفحات المتاحة:**
- `/` - الصفحة الرئيسية
- `/customers` - إدارة العملاء
- `/service-requests` - طلبات الصيانة
- `/scheduling` - جدولة المواعيد
- `/execution` - تنفيذ الصيانة
- `/reports` - التقارير والإحصائيات

---

## 🚨 حل المشاكل

### المشكلة: النظام لا يبدأ
```bash
# تنظيف وإعادة تثبيت
npm run clean

# فحص النظام
npm run check
```

### المشكلة: خطأ في قاعدة البيانات
1. تحقق من `.env.local`
2. تأكد من تشغيل مشروع Supabase
3. أعد تنفيذ SQL scripts

### المشكلة: صفحة فارغة
1. افتح Developer Tools (F12)
2. تحقق من وجود أخطاء في Console
3. تأكد من تحميل جميع الملفات

---

## 📞 الدعم

إذا واجهت مشاكل:

1. **شغل فحص النظام:**
   ```bash
   npm run check
   ```

2. **راجع الملفات:**
   - `SETUP.md` - دليل التثبيت الكامل
   - `QUICK-START.md` - البدء السريع
   - `.env.example` - مثال على ملف البيئة

3. **تحقق من:**
   - إصدار Node.js
   - اتصال الإنترنت
   - حالة مشروع Supabase

---

## 🎉 نجح التشغيل؟

إذا ظهرت الصفحة الرئيسية بنجاح:

1. **ابدأ بإضافة مهندسين** في قاعدة البيانات
2. **أضف قطع غيار** أساسية
3. **سجل عميل تجريبي** لاختبار النظام
4. **أنشئ طلب صيانة** تجريبي
5. **استكشف التقارير** والإحصائيات

---

**✨ مبروك! النظام يعمل بنجاح ✨**

النظام الآن جاهز لإدارة عمليات الصيانة بكفاءة عالية!
