#!/bin/bash

echo ""
echo "========================================"
echo "   نظام إدارة علاقات العملاء - الصيانة"
echo "========================================"
echo ""

# التحقق من وجود Node.js
if ! command -v node &> /dev/null; then
    echo "❌ خطأ: Node.js غير مثبت"
    echo "يرجى تثبيت Node.js من: https://nodejs.org"
    exit 1
fi

echo "✅ Node.js مثبت"
node --version

# التحقق من وجود ملف البيئة
if [ ! -f ".env.local" ]; then
    echo ""
    echo "⚠️  تحذير: ملف .env.local غير موجود"
    echo "يرجى إنشاء ملف .env.local وإضافة بيانات Supabase"
    echo "راجع ملف SETUP.md للتفاصيل"
    echo ""
    read -p "اضغط Enter للمتابعة..."
fi

# التحقق من وجود node_modules
if [ ! -d "node_modules" ]; then
    echo ""
    echo "📦 تثبيت التبعيات..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ فشل في تثبيت التبعيات"
        exit 1
    fi
    echo "✅ تم تثبيت التبعيات بنجاح"
fi

echo ""
echo "🚀 بدء تشغيل النظام..."
echo ""
echo "سيتم فتح النظام في المتصفح على العنوان:"
echo "http://localhost:3000"
echo ""
echo "للإيقاف: اضغط Ctrl+C"
echo ""

# تشغيل النظام
npm run dev
