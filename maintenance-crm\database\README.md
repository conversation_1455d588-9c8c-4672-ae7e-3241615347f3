# إعداد قاعدة البيانات - نظام إدارة الصيانة

## نظرة عامة

يحتوي هذا المجلد على ملفات SQL اللازمة لإعداد قاعدة البيانات لنظام إدارة علاقات العملاء للصيانة.

## الملفات

### 1. schema.sql
يحتوي على:
- إنشاء جميع الجداول الأساسية
- تعريف العلاقات والقيود
- إنشاء الفهارس لتحسين الأداء
- إعداد المشغلات (Triggers) لتحديث التواريخ تلقائياً

### 2. seed.sql
يحتوي على بيانات تجريبية لاختبار النظام:
- مهندسين عينة
- عملاء عينة
- أجهزة عينة
- طلبات صيانة عينة
- قطع غيار عينة

### 3. rls-policies.sql
يحتوي على:
- إعدادات الأمان على مستوى الصفوف (RLS)
- تعريف الأدوار والصلاحيات
- سياسات الوصول للبيانات
- Views مفيدة للاستعلامات
- Functions للإحصائيات

## خطوات الإعداد

### 1. إنشاء مشروع Supabase جديد
1. اذهب إلى [Supabase Dashboard](https://supabase.com/dashboard)
2. أنشئ مشروع جديد
3. احفظ URL المشروع و API Keys

### 2. تنفيذ ملفات SQL
قم بتنفيذ الملفات بالترتيب التالي في SQL Editor في Supabase:

```sql
-- 1. أولاً: إنشاء الجداول والهيكل الأساسي
-- انسخ والصق محتوى schema.sql

-- 2. ثانياً: إدراج البيانات التجريبية (اختياري)
-- انسخ والصق محتوى seed.sql

-- 3. ثالثاً: إعداد الأمان والصلاحيات
-- انسخ والصق محتوى rls-policies.sql
```

### 3. إعداد متغيرات البيئة
أنشئ ملف `.env.local` في جذر المشروع:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

### 4. إعداد المصادقة (Authentication)
في Supabase Dashboard:
1. اذهب إلى Authentication > Settings
2. فعّل Email authentication
3. أضف المجالات المسموحة في Site URL
4. اختياري: فعّل مقدمي خدمة إضافيين (Google, etc.)

## هيكل قاعدة البيانات

### الجداول الرئيسية

#### customers (العملاء)
- معلومات الاتصال الأساسية
- العنوان التفصيلي
- ملاحظات خاصة

#### devices (الأجهزة)
- معلومات الجهاز والموديل
- الرقم التسلسلي
- معلومات الضمان

#### engineers (المهندسين)
- معلومات المهندس
- التخصص
- حالة النشاط

#### service_requests (طلبات الصيانة)
- تفاصيل الطلب
- الأولوية والحالة
- ربط بالعميل والجهاز

#### scheduling (الجدولة)
- مواعيد الزيارات
- توكيل المهندسين
- تقدير الوقت

#### execution (التنفيذ)
- تفاصيل العمل المنجز
- الأجزاء المستخدمة
- التوقيعات والتقارير

#### follow_up (المتابعة)
- تقييم العميل
- حالة الدفع
- إغلاق الطلب

#### invoices (الفواتير)
- تفاصيل الفاتورة
- التكاليف
- حالة الدفع

#### parts (قطع الغيار)
- معلومات القطع
- الأسعار والمخزون

### العلاقات
- كل عميل يمكن أن يملك عدة أجهزة
- كل جهاز يمكن أن يكون له عدة طلبات صيانة
- كل طلب صيانة له جدولة واحدة وتنفيذ واحد ومتابعة واحدة
- كل تنفيذ يمكن أن يستخدم عدة قطع غيار

## الأمان والصلاحيات

### الأدوار المتاحة
- **admin_role**: صلاحية كاملة على جميع البيانات
- **customer_service_role**: إدارة العملاء وطلبات الصيانة
- **engineer_role**: عرض وتحديث المهام المكلف بها
- **viewer_role**: عرض البيانات فقط

### سياسات الأمان
- تم تفعيل RLS على جميع الجداول
- كل دور له صلاحيات محددة
- المهندسين يمكنهم فقط رؤية وتعديل مهامهم
- العملاء محميين بسياسات صارمة

## الاستعلامات المفيدة

### عرض طلبات الصيانة مع التفاصيل
```sql
SELECT * FROM service_requests_with_details 
WHERE status = 'قيد التنفيذ';
```

### إحصائيات لوحة التحكم
```sql
SELECT get_dashboard_stats();
```

### البحث عن عميل
```sql
SELECT * FROM customers 
WHERE full_name ILIKE '%اسم العميل%' 
   OR primary_phone LIKE '%رقم الهاتف%';
```

## الصيانة والتحسين

### النسخ الاحتياطي
- استخدم أدوات Supabase للنسخ الاحتياطي التلقائي
- قم بتصدير البيانات دورياً

### مراقبة الأداء
- راقب استخدام الفهارس
- تحقق من أداء الاستعلامات المعقدة
- استخدم EXPLAIN ANALYZE لتحليل الاستعلامات

### التحديثات
- احتفظ بنسخة من schema.sql محدثة
- وثق أي تغييرات في الهيكل
- اختبر التحديثات في بيئة التطوير أولاً

## استكشاف الأخطاء

### مشاكل شائعة
1. **خطأ في الصلاحيات**: تأكد من تطبيق RLS policies بشكل صحيح
2. **بطء في الاستعلامات**: تحقق من وجود الفهارس المناسبة
3. **مشاكل في العلاقات**: تأكد من صحة Foreign Keys

### سجلات النظام
- استخدم Supabase Dashboard لمراقبة السجلات
- فعّل Real-time للتحديثات الفورية
- راقب استخدام API limits

## الدعم
للحصول على المساعدة:
1. راجع [وثائق Supabase](https://supabase.com/docs)
2. تحقق من [مجتمع Supabase](https://github.com/supabase/supabase/discussions)
3. راجع ملفات المشروع والتعليقات في الكود
