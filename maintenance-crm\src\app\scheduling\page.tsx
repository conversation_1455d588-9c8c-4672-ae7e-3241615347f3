'use client'

import { useState, useEffect } from 'react'
import { Scheduling, ServiceRequest, Engineer } from '@/types'
import { SchedulingList } from '@/components/scheduling/scheduling-list'
import { SchedulingForm } from '@/components/scheduling/scheduling-form'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ArrowRight, Calendar, Clock, Users, AlertTriangle, CheckCircle } from 'lucide-react'
import { SchedulingAPI } from '@/lib/api/scheduling'
import { ServiceRequestsAPI } from '@/lib/api/service-requests'
import { EngineersAPI } from '@/lib/api/engineers'
import Link from 'next/link'

type ViewMode = 'list' | 'add' | 'edit' | 'view'

export default function SchedulingPage() {
  const [schedulings, setSchedulings] = useState<Scheduling[]>([])
  const [serviceRequests, setServiceRequests] = useState<ServiceRequest[]>([])
  const [engineers, setEngineers] = useState<Engineer[]>([])
  const [selectedScheduling, setSelectedScheduling] = useState<Scheduling | null>(null)
  const [viewMode, setViewMode] = useState<ViewMode>('list')
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [statistics, setStatistics] = useState({
    total: 0,
    today: 0,
    thisWeek: 0,
    overdue: 0,
    completed: 0
  })

  // Load data on component mount
  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      // Load schedulings
      const schedulingsResult = await SchedulingAPI.getAll()
      
      // Load available service requests (not completed or closed)
      const requestsResult = await ServiceRequestsAPI.getAll()
      const availableRequests = requestsResult.data?.filter(request => 
        !['مكتمل', 'مغلق'].includes(request.status)
      ) || []
      
      // Load active engineers
      const engineersResult = await EngineersAPI.getActive()
      
      if (schedulingsResult.error) {
        setError(schedulingsResult.error)
        // Fallback to mock data for development
        const mockSchedulings: Scheduling[] = [
          {
            id: '1',
            service_request_id: '1',
            engineer_id: '1',
            scheduled_date: '2025-01-15T10:00:00Z',
            estimated_duration: 120,
            engineer_notes: 'يحتاج فحص الضاغط والفلاتر',
            status: 'مؤكد',
            created_at: '2025-01-10T08:30:00Z',
            updated_at: '2025-01-10T08:30:00Z',
            engineer: {
              id: '1',
              full_name: 'أحمد محمد العلي',
              phone: '+966501234567',
              email: '<EMAIL>',
              specialization: 'أجهزة التكييف',
              is_active: true,
              created_at: '2024-01-01T00:00:00Z',
              updated_at: '2024-01-01T00:00:00Z'
            },
            service_request: {
              id: '1',
              customer_id: '1',
              device_id: '1',
              request_date: '2025-01-10T08:30:00Z',
              received_by: 'سارة أحمد - خدمة العملاء',
              request_source: 'هاتف',
              initial_description: 'المكيف لا يبرد بشكل جيد ويصدر أصوات غريبة',
              priority: 'عاجل',
              status: 'تم تحديد موعد',
              created_at: '2025-01-10T08:30:00Z',
              updated_at: '2025-01-10T08:30:00Z',
              customer: {
                id: '1',
                full_name: 'شركة الرياض للتجارة',
                primary_phone: '+966112345678',
                secondary_phone: '+966112345679',
                email: '<EMAIL>',
                detailed_address: 'شارع الملك فهد، حي العليا، الرياض',
                landmark: 'بجانب برج المملكة',
                contact_person: 'أحمد المدير العام',
                special_notes: 'يفضل الاتصال صباحاً',
                created_at: '2024-01-15T10:00:00Z',
                updated_at: '2024-01-15T10:00:00Z'
              }
            }
          }
        ]
        setSchedulings(mockSchedulings)
        calculateStatistics(mockSchedulings)
      } else {
        setSchedulings(schedulingsResult.data || [])
        calculateStatistics(schedulingsResult.data || [])
      }
      
      setServiceRequests(availableRequests)
      setEngineers(engineersResult.data || [])
      
    } catch (err) {
      console.error('Error loading data:', err)
      setError('حدث خطأ في تحميل البيانات')
    } finally {
      setIsLoading(false)
    }
  }

  const calculateStatistics = (schedulings: Scheduling[]) => {
    const now = new Date()
    const today = new Date(now.setHours(0, 0, 0, 0))
    const endOfToday = new Date(now.setHours(23, 59, 59, 999))
    const weekFromNow = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
    
    const stats = {
      total: schedulings.length,
      today: schedulings.filter(s => {
        const scheduleDate = new Date(s.scheduled_date)
        return scheduleDate >= today && scheduleDate <= endOfToday
      }).length,
      thisWeek: schedulings.filter(s => {
        const scheduleDate = new Date(s.scheduled_date)
        return scheduleDate >= today && scheduleDate <= weekFromNow
      }).length,
      overdue: schedulings.filter(s => {
        const scheduleDate = new Date(s.scheduled_date)
        return scheduleDate < today && !['مكتمل', 'ملغي'].includes(s.status)
      }).length,
      completed: schedulings.filter(s => s.status === 'مكتمل').length
    }
    setStatistics(stats)
  }

  const handleAddScheduling = async (data: any) => {
    const result = await SchedulingAPI.create(data)
    
    if (result.error) {
      alert(`خطأ: ${result.error}`)
      return
    }
    
    if (result.data) {
      const updatedSchedulings = [result.data, ...schedulings]
      setSchedulings(updatedSchedulings)
      calculateStatistics(updatedSchedulings)
      setViewMode('list')
      alert(result.message || 'تم إنشاء الجدولة بنجاح!')
      
      // Reload service requests to update available ones
      loadData()
    }
  }

  const handleEditScheduling = async (data: any) => {
    if (!selectedScheduling) return
    
    const result = await SchedulingAPI.update(selectedScheduling.id, data)
    
    if (result.error) {
      alert(`خطأ: ${result.error}`)
      return
    }
    
    if (result.data) {
      const updatedSchedulings = schedulings.map(scheduling => 
        scheduling.id === selectedScheduling.id ? result.data! : scheduling
      )
      setSchedulings(updatedSchedulings)
      calculateStatistics(updatedSchedulings)
      setSelectedScheduling(result.data)
      setViewMode('view')
      alert(result.message || 'تم تحديث الجدولة بنجاح!')
    }
  }

  const handleViewScheduling = (scheduling: Scheduling) => {
    setSelectedScheduling(scheduling)
    setViewMode('view')
  }

  const handleEditMode = (scheduling: Scheduling) => {
    setSelectedScheduling(scheduling)
    setViewMode('edit')
  }

  const handleBackToList = () => {
    setSelectedScheduling(null)
    setViewMode('list')
  }

  const renderContent = () => {
    switch (viewMode) {
      case 'add':
        return (
          <SchedulingForm
            serviceRequests={serviceRequests}
            engineers={engineers}
            onSubmit={handleAddScheduling}
            onCancel={handleBackToList}
          />
        )
      
      case 'edit':
        return selectedScheduling ? (
          <SchedulingForm
            initialData={selectedScheduling}
            serviceRequests={serviceRequests}
            engineers={engineers}
            onSubmit={handleEditScheduling}
            onCancel={handleBackToList}
          />
        ) : null
      
      case 'view':
        // سيتم تطوير مكون عرض تفاصيل الجدولة لاحقاً
        return (
          <div className="text-center py-12">
            <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              عرض تفاصيل الجدولة
            </h3>
            <p className="text-gray-500 mb-4">
              سيتم تطوير هذه الميزة قريباً
            </p>
            <Button onClick={handleBackToList}>
              العودة للقائمة
            </Button>
          </div>
        )
      
      default:
        return (
          <SchedulingList
            schedulings={schedulings}
            onEdit={handleEditMode}
            onView={handleViewScheduling}
            onAdd={() => setViewMode('add')}
            isLoading={isLoading}
          />
        )
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center gap-4">
              {viewMode !== 'list' && (
                <Button
                  variant="ghost"
                  onClick={handleBackToList}
                  className="flex items-center gap-2"
                >
                  <ArrowRight className="h-4 w-4" />
                  العودة للقائمة
                </Button>
              )}
              <div className="flex items-center gap-3">
                <Calendar className="h-8 w-8 text-blue-600" />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    {viewMode === 'add' && 'جدولة موعد جديد'}
                    {viewMode === 'edit' && 'تعديل الجدولة'}
                    {viewMode === 'view' && 'تفاصيل الجدولة'}
                    {viewMode === 'list' && 'جدولة الصيانة'}
                  </h1>
                  <p className="text-gray-600">
                    {viewMode === 'add' && 'إنشاء جدولة موعد صيانة جديد'}
                    {viewMode === 'edit' && 'تعديل بيانات الجدولة'}
                    {viewMode === 'view' && 'عرض تفاصيل الجدولة'}
                    {viewMode === 'list' && 'عرض وإدارة جدولة مواعيد الصيانة'}
                  </p>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <Link href="/">
                <Button variant="outline">
                  العودة للرئيسية
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Statistics Cards - only show in list view */}
        {viewMode === 'list' && !isLoading && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">إجمالي الجدولات</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{statistics.total}</div>
                <p className="text-xs text-muted-foreground">جدولة</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">اليوم</CardTitle>
                <Clock className="h-4 w-4 text-blue-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">{statistics.today}</div>
                <p className="text-xs text-muted-foreground">موعد اليوم</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">هذا الأسبوع</CardTitle>
                <Calendar className="h-4 w-4 text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{statistics.thisWeek}</div>
                <p className="text-xs text-muted-foreground">موعد هذا الأسبوع</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">متأخرة</CardTitle>
                <AlertTriangle className="h-4 w-4 text-red-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">{statistics.overdue}</div>
                <p className="text-xs text-muted-foreground">موعد متأخر</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">مكتملة</CardTitle>
                <CheckCircle className="h-4 w-4 text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{statistics.completed}</div>
                <p className="text-xs text-muted-foreground">موعد مكتمل</p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Error Message */}
        {error && viewMode === 'list' && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-yellow-600" />
              <p className="text-yellow-800">
                تعذر الاتصال بقاعدة البيانات. يتم عرض بيانات تجريبية.
              </p>
            </div>
          </div>
        )}

        {/* Main Content Area */}
        {renderContent()}
      </div>
    </div>
  )
}
