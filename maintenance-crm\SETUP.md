# دليل التثبيت والتشغيل - نظام إدارة علاقات العملاء للصيانة

## 🚀 نظرة عامة

نظام متكامل لإدارة علاقات العملاء وطلبات الصيانة مع دعم كامل للغة العربية وواجهة مستخدم حديثة.

### الميزات الرئيسية:
- ✅ إدارة العملاء والأجهزة
- ✅ تسجيل ومتابعة طلبات الصيانة
- ✅ جدولة المواعيد وتوكيل المهندسين
- ✅ تسجيل تنفيذ الصيانة وإدارة قطع الغيار
- ✅ لوحة معلومات شاملة وتقارير مفصلة
- ✅ متابعة رضا العملاء
- ✅ دعم كامل للغة العربية مع RTL

## 📋 متطلبات النظام

- **Node.js** 18.0 أو أحدث
- **npm** أو **yarn**
- حساب **Supabase** (مجاني)

## ⚡ خطوات التثبيت

### 1. تحضير المشروع

```bash
# استنساخ المشروع (إذا كان في Git)
git clone <repository-url>
cd maintenance-crm

# أو إنشاء مجلد جديد ونسخ الملفات
mkdir maintenance-crm
cd maintenance-crm
# انسخ جميع ملفات المشروع هنا
```

### 2. تثبيت التبعيات

```bash
npm install
```

أو إذا كنت تفضل yarn:

```bash
yarn install
```

### 3. إعداد قاعدة البيانات (Supabase)

#### أ. إنشاء مشروع Supabase

1. اذهب إلى [supabase.com](https://supabase.com)
2. أنشئ حساب جديد أو سجل الدخول
3. انقر على "New Project"
4. اختر منظمة أو أنشئ واحدة جديدة
5. أدخل اسم المشروع وكلمة مرور قاعدة البيانات
6. اختر المنطقة الأقرب لك
7. انقر على "Create new project"
8. انتظر حتى يكتمل إعداد المشروع (2-3 دقائق)

#### ب. الحصول على بيانات الاتصال

1. في لوحة تحكم Supabase، اذهب إلى **Settings** > **API**
2. انسخ القيم التالية:
   - **Project URL** (URL)
   - **anon public** key (API Key)

#### ج. إنشاء ملف البيئة

```bash
# إنشاء ملف البيئة
cp .env.example .env.local
```

أو أنشئ ملف `.env.local` يدوياً وأضف:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
```

**مثال:**
```env
NEXT_PUBLIC_SUPABASE_URL=https://abcdefghijklmnop.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 4. إعداد قاعدة البيانات

#### أ. تشغيل SQL Scripts

1. في لوحة تحكم Supabase، اذهب إلى **SQL Editor**
2. انقر على "New query"
3. انسخ والصق محتوى ملف `database/schema.sql`
4. انقر على "Run" لتنفيذ الاستعلام
5. كرر العملية مع ملف `database/rls-policies.sql`
6. أخيراً، نفذ ملف `database/seed.sql` لإضافة بيانات تجريبية

#### ب. التحقق من إنشاء الجداول

1. اذهب إلى **Table Editor**
2. تأكد من وجود الجداول التالية:
   - customers
   - devices
   - service_requests
   - engineers
   - scheduling
   - execution
   - parts
   - execution_parts

### 5. تشغيل النظام

```bash
npm run dev
```

أو:

```bash
yarn dev
```

### 6. الوصول للنظام

افتح المتصفح واذهب إلى: **http://localhost:3000**

## 🎯 اختبار النظام

### 1. التحقق من الصفحة الرئيسية
- يجب أن تظهر الصفحة الرئيسية بالعربية
- تأكد من وجود 6 بطاقات للوحدات المختلفة

### 2. اختبار إدارة العملاء
- اذهب إلى "العملاء"
- جرب إضافة عميل جديد
- تأكد من حفظ البيانات

### 3. اختبار طلبات الصيانة
- اذهب إلى "طلبات الصيانة"
- أنشئ طلب صيانة جديد
- اربطه بعميل موجود

### 4. اختبار التقارير
- اذهب إلى "التقارير"
- تحقق من ظهور الإحصائيات
- جرب توليد تقرير

## 🔧 إعدادات إضافية

### إضافة مهندسين

1. اذهب إلى Supabase > Table Editor > engineers
2. أضف مهندسين جدد:

```sql
INSERT INTO engineers (full_name, phone, email, specialization, is_active) VALUES
('أحمد محمد العلي', '+966501234567', '<EMAIL>', 'أجهزة التكييف', true),
('سارة أحمد محمد', '+966507654321', '<EMAIL>', 'الأجهزة المنزلية', true),
('خالد عبدالله', '+966509876543', '<EMAIL>', 'أجهزة التبريد', true);
```

### إضافة قطع غيار

```sql
INSERT INTO parts (part_name, part_number, description, unit_price, stock_quantity) VALUES
('فلتر هواء', 'FILTER-001', 'فلتر هواء للمكيفات', 25.00, 50),
('غاز تبريد R410A', 'GAS-R410A', 'غاز تبريد للمكيفات الحديثة', 150.00, 20),
('ضاغط مكيف', 'COMP-001', 'ضاغط للمكيفات السبليت', 800.00, 5);
```

## 🚨 حل المشاكل الشائعة

### مشكلة: خطأ في الاتصال بقاعدة البيانات

**الحل:**
1. تأكد من صحة URL و API Key في `.env.local`
2. تأكد من تشغيل مشروع Supabase
3. تحقق من عدم وجود مسافات إضافية في المتغيرات

### مشكلة: الصفحات لا تظهر بالعربية

**الحل:**
1. تأكد من تثبيت الخطوط العربية
2. تحقق من إعدادات RTL في `tailwind.config.js`
3. امسح cache المتصفح

### مشكلة: بطء في التحميل

**الحل:**
1. تأكد من استخدام أحدث إصدار من Node.js
2. أعد تشغيل خادم التطوير
3. تحقق من سرعة الإنترنت

### مشكلة: خطأ في تثبيت التبعيات

**الحل:**
```bash
# احذف node_modules و package-lock.json
rm -rf node_modules package-lock.json

# أعد التثبيت
npm install
```

## 📱 الاستخدام على الأجهزة المحمولة

النظام متجاوب ويعمل على:
- 📱 الهواتف الذكية
- 📱 الأجهزة اللوحية  
- 💻 أجهزة الكمبيوتر المحمولة
- 🖥️ أجهزة سطح المكتب

## 🔒 الأمان

- ✅ Row Level Security (RLS) مفعل
- ✅ تشفير البيانات
- ✅ التحقق من صحة المدخلات
- ✅ حماية من SQL Injection

## 📞 الدعم

إذا واجهت أي مشاكل:

1. راجع قسم "حل المشاكل الشائعة" أعلاه
2. تحقق من console المتصفح للأخطاء
3. راجع logs Supabase
4. تأكد من تطبيق جميع الخطوات بالترتيب

## 🎉 تهانينا!

النظام الآن جاهز للاستخدام! 

### الخطوات التالية:
1. أضف مهندسين وقطع غيار
2. ابدأ بتسجيل العملاء
3. سجل طلبات الصيانة
4. استخدم التقارير لمتابعة الأداء

---

**نظام تم تطويره بعناية فائقة لخدمة احتياجات قسم الصيانة 🔧**
