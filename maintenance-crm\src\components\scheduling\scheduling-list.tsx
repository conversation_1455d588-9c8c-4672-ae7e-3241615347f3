'use client'

import { useState } from 'react'
import { Scheduling } from '@/types'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  Search, 
  Filter,
  Eye, 
  Edit, 
  Plus,
  Calendar,
  Clock,
  User,
  MapPin,
  Phone,
  AlertCircle
} from 'lucide-react'
import { formatDateTime, formatDate, formatTime } from '@/lib/utils'

interface SchedulingListProps {
  schedulings: Scheduling[]
  onEdit: (scheduling: Scheduling) => void
  onView: (scheduling: Scheduling) => void
  onAdd: () => void
  isLoading?: boolean
}

interface Filters {
  search: string
  status: string
  engineer: string
  date: string
}

const SCHEDULING_STATUS = [
  { value: 'مجدول', label: 'مجدول', color: 'bg-blue-100 text-blue-800' },
  { value: 'مؤكد', label: 'مؤكد', color: 'bg-green-100 text-green-800' },
  { value: 'في الطريق', label: 'في الطريق', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'وصل', label: 'وصل', color: 'bg-purple-100 text-purple-800' },
  { value: 'مكتمل', label: 'مكتمل', color: 'bg-green-100 text-green-800' },
  { value: 'ملغي', label: 'ملغي', color: 'bg-red-100 text-red-800' },
]

export function SchedulingList({ 
  schedulings, 
  onEdit, 
  onView, 
  onAdd, 
  isLoading 
}: SchedulingListProps) {
  const [filters, setFilters] = useState<Filters>({
    search: '',
    status: 'all',
    engineer: 'all',
    date: ''
  })
  const [showFilters, setShowFilters] = useState(false)

  // Get unique engineers for filter
  const uniqueEngineers = Array.from(
    new Set(schedulings.map(s => s.engineer?.id).filter(Boolean))
  ).map(id => schedulings.find(s => s.engineer?.id === id)?.engineer).filter(Boolean)

  // Filter schedulings based on current filters
  const filteredSchedulings = schedulings.filter(scheduling => {
    const matchesSearch = !filters.search.trim() || 
      scheduling.service_request?.initial_description?.toLowerCase().includes(filters.search.toLowerCase()) ||
      scheduling.service_request?.customer?.full_name?.toLowerCase().includes(filters.search.toLowerCase()) ||
      scheduling.engineer?.full_name?.toLowerCase().includes(filters.search.toLowerCase()) ||
      scheduling.engineer_notes?.toLowerCase().includes(filters.search.toLowerCase())

    const matchesStatus = filters.status === 'all' || scheduling.status === filters.status
    const matchesEngineer = filters.engineer === 'all' || scheduling.engineer_id === filters.engineer
    
    const matchesDate = !filters.date || 
      new Date(scheduling.scheduled_date).toDateString() === new Date(filters.date).toDateString()

    return matchesSearch && matchesStatus && matchesEngineer && matchesDate
  })

  const getStatusColor = (status: string) => {
    const statusObj = SCHEDULING_STATUS.find(s => s.value === status)
    return statusObj?.color || 'bg-gray-100 text-gray-800'
  }

  const getPriorityColor = (priority: string) => {
    const colors: Record<string, string> = {
      'عاجل': 'bg-red-100 text-red-800',
      'متوسط': 'bg-yellow-100 text-yellow-800',
      'منخفض': 'bg-green-100 text-green-800',
    }
    return colors[priority] || 'bg-gray-100 text-gray-800'
  }

  const isOverdue = (scheduledDate: string, status: string) => {
    const now = new Date()
    const scheduled = new Date(scheduledDate)
    return scheduled < now && !['مكتمل', 'ملغي'].includes(status)
  }

  const isToday = (scheduledDate: string) => {
    const today = new Date()
    const scheduled = new Date(scheduledDate)
    return today.toDateString() === scheduled.toDateString()
  }

  const resetFilters = () => {
    setFilters({
      search: '',
      status: 'all',
      engineer: 'all',
      date: ''
    })
  }

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    if (hours === 0) return `${mins} دقيقة`
    if (mins === 0) return `${hours} ساعة`
    return `${hours} ساعة و ${mins} دقيقة`
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="space-y-3">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header with Search and Filters */}
      <div className="flex flex-col gap-4">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex-1 max-w-md">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="البحث في الجدولة..."
                value={filters.search}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                className="pr-10"
              />
            </div>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2"
            >
              <Filter className="h-4 w-4" />
              المرشحات
            </Button>
            <Button onClick={onAdd} className="whitespace-nowrap">
              <Plus className="h-4 w-4 ml-2" />
              جدولة جديدة
            </Button>
          </div>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <Card>
            <CardContent className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">الحالة</label>
                  <Select
                    value={filters.status}
                    onValueChange={(value) => setFilters(prev => ({ ...prev, status: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">جميع الحالات</SelectItem>
                      {SCHEDULING_STATUS.map((status) => (
                        <SelectItem key={status.value} value={status.value}>
                          {status.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">المهندس</label>
                  <Select
                    value={filters.engineer}
                    onValueChange={(value) => setFilters(prev => ({ ...prev, engineer: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">جميع المهندسين</SelectItem>
                      {uniqueEngineers.map((engineer) => (
                        <SelectItem key={engineer!.id} value={engineer!.id}>
                          {engineer!.full_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">التاريخ</label>
                  <Input
                    type="date"
                    value={filters.date}
                    onChange={(e) => setFilters(prev => ({ ...prev, date: e.target.value }))}
                  />
                </div>

                <div className="flex items-end">
                  <Button variant="outline" onClick={resetFilters} className="w-full">
                    إعادة تعيين
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Results Summary */}
      <div className="text-sm text-gray-600">
        {filters.search || filters.status !== 'all' || filters.engineer !== 'all' || filters.date ? (
          <span>
            تم العثور على {filteredSchedulings.length} جدولة من أصل {schedulings.length}
          </span>
        ) : (
          <span>إجمالي الجدولات: {schedulings.length}</span>
        )}
      </div>

      {/* Scheduling Cards */}
      <div className="space-y-4">
        {filteredSchedulings.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <Calendar className="h-12 w-12 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {filters.search || filters.status !== 'all' || filters.engineer !== 'all' || filters.date
                ? 'لم يتم العثور على نتائج'
                : 'لا توجد جدولات'
              }
            </h3>
            <p className="text-gray-500 mb-4">
              {filters.search || filters.status !== 'all' || filters.engineer !== 'all' || filters.date
                ? 'جرب تغيير المرشحات أو كلمات البحث'
                : 'ابدأ بإنشاء جدولة جديدة'
              }
            </p>
            {(!filters.search && filters.status === 'all' && filters.engineer === 'all' && !filters.date) && (
              <Button onClick={onAdd}>
                <Plus className="h-4 w-4 ml-2" />
                جدولة جديدة
              </Button>
            )}
          </div>
        ) : (
          filteredSchedulings.map((scheduling) => (
            <Card 
              key={scheduling.id} 
              className={`hover:shadow-lg transition-shadow ${
                isOverdue(scheduling.scheduled_date, scheduling.status) 
                  ? 'border-red-200 bg-red-50' 
                  : isToday(scheduling.scheduled_date) 
                    ? 'border-blue-200 bg-blue-50' 
                    : ''
              }`}
            >
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <Badge className={getStatusColor(scheduling.status)}>
                        {scheduling.status}
                      </Badge>
                      {scheduling.service_request && (
                        <Badge className={getPriorityColor(scheduling.service_request.priority)}>
                          {scheduling.service_request.priority}
                        </Badge>
                      )}
                      {isOverdue(scheduling.scheduled_date, scheduling.status) && (
                        <Badge className="bg-red-100 text-red-800">
                          <AlertCircle className="h-3 w-3 ml-1" />
                          متأخر
                        </Badge>
                      )}
                      {isToday(scheduling.scheduled_date) && (
                        <Badge className="bg-blue-100 text-blue-800">
                          اليوم
                        </Badge>
                      )}
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                      {scheduling.service_request?.initial_description || 'طلب صيانة'}
                    </h3>
                  </div>
                  <div className="flex gap-2 ml-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onView(scheduling)}
                    >
                      <Eye className="h-4 w-4 ml-1" />
                      عرض
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onEdit(scheduling)}
                    >
                      <Edit className="h-4 w-4 ml-1" />
                      تعديل
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 mb-4">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="font-medium">{formatDate(scheduling.scheduled_date)}</p>
                      <p className="text-xs">{formatTime(scheduling.scheduled_date)}</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="font-medium">{scheduling.engineer?.full_name}</p>
                      <p className="text-xs">{scheduling.engineer?.phone}</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="font-medium">{formatDuration(scheduling.estimated_duration)}</p>
                      <p className="text-xs">المدة المقدرة</p>
                    </div>
                  </div>
                </div>

                {scheduling.service_request?.customer && (
                  <div className="border-t pt-4">
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-gray-400" />
                        <span>{scheduling.service_request.customer.full_name}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-gray-400" />
                        <span>{scheduling.service_request.customer.primary_phone}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-gray-400" />
                        <span className="line-clamp-1">{scheduling.service_request.customer.detailed_address}</span>
                      </div>
                    </div>
                  </div>
                )}

                {scheduling.engineer_notes && (
                  <div className="mt-4 bg-gray-50 rounded-lg p-3">
                    <p className="text-sm text-gray-700">
                      <span className="font-medium">ملاحظات للمهندس:</span> {scheduling.engineer_notes}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  )
}
