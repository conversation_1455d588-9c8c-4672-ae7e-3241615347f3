'use client'

import { useState, useEffect } from 'react'
import { Execution, Scheduling, Part, ExecutionPart } from '@/types'
import { ExecutionList } from '@/components/execution/execution-list'
import { ExecutionForm } from '@/components/execution/execution-form'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ArrowRight, Wrench, Clock, CheckCircle, AlertTriangle, FileText } from 'lucide-react'
import { ExecutionAPI } from '@/lib/api/execution'
import { SchedulingAPI } from '@/lib/api/scheduling'
import { PartsAPI } from '@/lib/api/parts'
import Link from 'next/link'

type ViewMode = 'list' | 'add' | 'edit' | 'view'

export default function ExecutionPage() {
  const [executions, setExecutions] = useState<Execution[]>([])
  const [availableSchedulings, setAvailableSchedulings] = useState<Scheduling[]>([])
  const [availableParts, setAvailableParts] = useState<Part[]>([])
  const [selectedExecution, setSelectedExecution] = useState<Execution | null>(null)
  const [viewMode, setViewMode] = useState<ViewMode>('list')
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [statistics, setStatistics] = useState({
    total: 0,
    completed: 0,
    inProgress: 0,
    notStarted: 0,
    averageDuration: 0
  })

  // Load data on component mount
  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      // Load executions
      const executionsResult = await ExecutionAPI.getAll()
      
      // Load completed schedulings that don't have executions yet
      const schedulingsResult = await SchedulingAPI.getAll()
      const completedSchedulings = schedulingsResult.data?.filter(scheduling => 
        scheduling.status === 'مكتمل' || scheduling.status === 'وصل'
      ) || []
      
      // Load available parts
      const partsResult = await PartsAPI.getAll()
      
      // Load statistics
      const statsResult = await ExecutionAPI.getStatistics()
      
      if (executionsResult.error) {
        setError(executionsResult.error)
        // Fallback to mock data for development
        const mockExecutions: Execution[] = [
          {
            id: '1',
            service_request_id: '1',
            engineer_id: '1',
            arrival_time: '2025-01-15T10:00:00Z',
            start_time: '2025-01-15T10:15:00Z',
            end_time: '2025-01-15T12:30:00Z',
            technical_description: 'تم فحص المكيف ووجد أن الضاغط يعمل بشكل ضعيف والفلاتر متسخة جداً',
            repair_actions: 'تم تنظيف الفلاتر وإضافة غاز التبريد وفحص الدائرة الكهربائية',
            failure_cause: 'عدم الصيانة الدورية وتراكم الأتربة',
            tools_used: 'مفكات متنوعة، جهاز قياس كهربائي، مضخة تفريغ',
            future_recommendations: 'ينصح بتنظيف الفلاتر كل 3 أشهر وعمل صيانة دورية كل 6 أشهر',
            received_by_customer: 'أحمد المدير العام',
            device_status_after: 'يعمل بكفاءة عالية',
            customer_signature: 'أحمد محمد',
            technical_report: 'الجهاز يعمل بكفاءة عالية بعد الصيانة',
            created_at: '2025-01-15T10:00:00Z',
            updated_at: '2025-01-15T12:30:00Z',
            engineer: {
              id: '1',
              full_name: 'أحمد محمد العلي',
              phone: '+966501234567',
              email: '<EMAIL>',
              specialization: 'أجهزة التكييف',
              is_active: true,
              created_at: '2024-01-01T00:00:00Z',
              updated_at: '2024-01-01T00:00:00Z'
            },
            service_request: {
              id: '1',
              customer_id: '1',
              device_id: '1',
              request_date: '2025-01-10T08:30:00Z',
              received_by: 'سارة أحمد - خدمة العملاء',
              request_source: 'هاتف',
              initial_description: 'المكيف لا يبرد بشكل جيد ويصدر أصوات غريبة',
              priority: 'عاجل',
              status: 'مكتمل',
              created_at: '2025-01-10T08:30:00Z',
              updated_at: '2025-01-15T12:30:00Z',
              customer: {
                id: '1',
                full_name: 'شركة الرياض للتجارة',
                primary_phone: '+966112345678',
                secondary_phone: '+966112345679',
                email: '<EMAIL>',
                detailed_address: 'شارع الملك فهد، حي العليا، الرياض',
                landmark: 'بجانب برج المملكة',
                contact_person: 'أحمد المدير العام',
                special_notes: 'يفضل الاتصال صباحاً',
                created_at: '2024-01-15T10:00:00Z',
                updated_at: '2024-01-15T10:00:00Z'
              },
              device: {
                id: '1',
                customer_id: '1',
                device_name: 'مكيف سبليت',
                model: 'Samsung AR24',
                serial_number: 'SAM2024001',
                purchase_date: '2024-01-15',
                warranty_end_date: '2026-01-15',
                original_invoice_number: 'INV-2024-001',
                created_at: '2024-01-15T10:00:00Z',
                updated_at: '2024-01-15T10:00:00Z'
              }
            }
          }
        ]
        setExecutions(mockExecutions)
        setStatistics({
          total: 1,
          completed: 1,
          inProgress: 0,
          notStarted: 0,
          averageDuration: 2.25
        })
      } else {
        setExecutions(executionsResult.data || [])
        if (statsResult.data) {
          setStatistics(statsResult.data)
        }
      }
      
      setAvailableSchedulings(completedSchedulings)
      setAvailableParts(partsResult.data || [])
      
    } catch (err) {
      console.error('Error loading data:', err)
      setError('حدث خطأ في تحميل البيانات')
    } finally {
      setIsLoading(false)
    }
  }

  const handleAddExecution = async (data: any, usedParts: ExecutionPart[]) => {
    const result = await ExecutionAPI.create(data, usedParts)
    
    if (result.error) {
      alert(`خطأ: ${result.error}`)
      return
    }
    
    if (result.data) {
      const updatedExecutions = [result.data, ...executions]
      setExecutions(updatedExecutions)
      setViewMode('list')
      alert(result.message || 'تم إنشاء تقرير التنفيذ بنجاح!')
      
      // Reload data to update available schedulings
      loadData()
    }
  }

  const handleEditExecution = async (data: any, usedParts: ExecutionPart[]) => {
    if (!selectedExecution) return
    
    const result = await ExecutionAPI.update(selectedExecution.id, data, usedParts)
    
    if (result.error) {
      alert(`خطأ: ${result.error}`)
      return
    }
    
    if (result.data) {
      const updatedExecutions = executions.map(execution => 
        execution.id === selectedExecution.id ? result.data! : execution
      )
      setExecutions(updatedExecutions)
      setSelectedExecution(result.data)
      setViewMode('view')
      alert(result.message || 'تم تحديث تقرير التنفيذ بنجاح!')
    }
  }

  const handleViewExecution = (execution: Execution) => {
    setSelectedExecution(execution)
    setViewMode('view')
  }

  const handleEditMode = (execution: Execution) => {
    setSelectedExecution(execution)
    setViewMode('edit')
  }

  const handleBackToList = () => {
    setSelectedExecution(null)
    setViewMode('list')
  }

  const renderContent = () => {
    switch (viewMode) {
      case 'add':
        return (
          <ExecutionForm
            availableParts={availableParts}
            onSubmit={handleAddExecution}
            onCancel={handleBackToList}
          />
        )
      
      case 'edit':
        return selectedExecution ? (
          <ExecutionForm
            initialData={selectedExecution}
            availableParts={availableParts}
            onSubmit={handleEditExecution}
            onCancel={handleBackToList}
          />
        ) : null
      
      case 'view':
        // سيتم تطوير مكون عرض تفاصيل التنفيذ لاحقاً
        return (
          <div className="text-center py-12">
            <Wrench className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              عرض تفاصيل تقرير التنفيذ
            </h3>
            <p className="text-gray-500 mb-4">
              سيتم تطوير هذه الميزة قريباً
            </p>
            <Button onClick={handleBackToList}>
              العودة للقائمة
            </Button>
          </div>
        )
      
      default:
        return (
          <ExecutionList
            executions={executions}
            onEdit={handleEditMode}
            onView={handleViewExecution}
            onAdd={() => setViewMode('add')}
            isLoading={isLoading}
          />
        )
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center gap-4">
              {viewMode !== 'list' && (
                <Button
                  variant="ghost"
                  onClick={handleBackToList}
                  className="flex items-center gap-2"
                >
                  <ArrowRight className="h-4 w-4" />
                  العودة للقائمة
                </Button>
              )}
              <div className="flex items-center gap-3">
                <Wrench className="h-8 w-8 text-blue-600" />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    {viewMode === 'add' && 'تقرير تنفيذ جديد'}
                    {viewMode === 'edit' && 'تعديل تقرير التنفيذ'}
                    {viewMode === 'view' && 'تفاصيل تقرير التنفيذ'}
                    {viewMode === 'list' && 'تنفيذ الصيانة'}
                  </h1>
                  <p className="text-gray-600">
                    {viewMode === 'add' && 'إنشاء تقرير تنفيذ صيانة جديد'}
                    {viewMode === 'edit' && 'تعديل تفاصيل تقرير التنفيذ'}
                    {viewMode === 'view' && 'عرض تفاصيل تقرير التنفيذ'}
                    {viewMode === 'list' && 'عرض وإدارة تقارير تنفيذ الصيانة'}
                  </p>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <Link href="/">
                <Button variant="outline">
                  العودة للرئيسية
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Statistics Cards - only show in list view */}
        {viewMode === 'list' && !isLoading && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">إجمالي التقارير</CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{statistics.total}</div>
                <p className="text-xs text-muted-foreground">تقرير تنفيذ</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">مكتملة</CardTitle>
                <CheckCircle className="h-4 w-4 text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{statistics.completed}</div>
                <p className="text-xs text-muted-foreground">تقرير مكتمل</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">قيد التنفيذ</CardTitle>
                <Clock className="h-4 w-4 text-blue-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">{statistics.inProgress}</div>
                <p className="text-xs text-muted-foreground">قيد العمل</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">لم تبدأ</CardTitle>
                <AlertTriangle className="h-4 w-4 text-yellow-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">{statistics.notStarted}</div>
                <p className="text-xs text-muted-foreground">في الانتظار</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">متوسط المدة</CardTitle>
                <Clock className="h-4 w-4 text-purple-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-purple-600">{statistics.averageDuration}</div>
                <p className="text-xs text-muted-foreground">ساعة</p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Error Message */}
        {error && viewMode === 'list' && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-yellow-600" />
              <p className="text-yellow-800">
                تعذر الاتصال بقاعدة البيانات. يتم عرض بيانات تجريبية.
              </p>
            </div>
          </div>
        )}

        {/* Main Content Area */}
        {renderContent()}
      </div>
    </div>
  )
}
