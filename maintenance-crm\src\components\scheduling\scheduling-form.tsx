'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ServiceRequest, Engineer, Scheduling } from '@/types'
import { Save, X, Calendar, Clock, User, AlertCircle } from 'lucide-react'
import { formatDateTime, formatDate } from '@/lib/utils'

const schedulingSchema = z.object({
  service_request_id: z.string().min(1, 'يجب اختيار طلب الصيانة'),
  engineer_id: z.string().min(1, 'يجب اختيار المهندس'),
  scheduled_date: z.string().min(1, 'تاريخ ووقت الموعد مطلوب'),
  estimated_duration: z.number().min(15, 'المدة المقدرة يجب أن تكون 15 دقيقة على الأقل').max(480, 'المدة المقدرة لا يمكن أن تتجاوز 8 ساعات'),
  engineer_notes: z.string().optional(),
  status: z.enum(['مجدول', 'مؤكد', 'في الطريق', 'وصل', 'مكتمل', 'ملغي']).optional(),
})

interface SchedulingFormData {
  service_request_id: string
  engineer_id: string
  scheduled_date: string
  estimated_duration: number
  engineer_notes?: string
  status?: string
}

interface SchedulingFormProps {
  initialData?: Partial<SchedulingFormData>
  serviceRequests?: ServiceRequest[]
  engineers?: Engineer[]
  onSubmit: (data: SchedulingFormData) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}

const SCHEDULING_STATUS = [
  { value: 'مجدول', label: 'مجدول', color: 'bg-blue-100 text-blue-800' },
  { value: 'مؤكد', label: 'مؤكد', color: 'bg-green-100 text-green-800' },
  { value: 'في الطريق', label: 'في الطريق', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'وصل', label: 'وصل', color: 'bg-purple-100 text-purple-800' },
  { value: 'مكتمل', label: 'مكتمل', color: 'bg-green-100 text-green-800' },
  { value: 'ملغي', label: 'ملغي', color: 'bg-red-100 text-red-800' },
]

const DURATION_OPTIONS = [
  { value: 30, label: '30 دقيقة' },
  { value: 60, label: 'ساعة واحدة' },
  { value: 90, label: 'ساعة ونصف' },
  { value: 120, label: 'ساعتان' },
  { value: 180, label: '3 ساعات' },
  { value: 240, label: '4 ساعات' },
  { value: 300, label: '5 ساعات' },
  { value: 360, label: '6 ساعات' },
  { value: 480, label: '8 ساعات' },
]

export function SchedulingForm({ 
  initialData, 
  serviceRequests = [], 
  engineers = [], 
  onSubmit, 
  onCancel, 
  isLoading 
}: SchedulingFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [selectedRequest, setSelectedRequest] = useState<ServiceRequest | null>(null)
  const [selectedEngineer, setSelectedEngineer] = useState<Engineer | null>(null)

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    reset,
    watch,
    setValue
  } = useForm<SchedulingFormData>({
    resolver: zodResolver(schedulingSchema),
    defaultValues: initialData || {
      service_request_id: '',
      engineer_id: '',
      scheduled_date: '',
      estimated_duration: 120,
      engineer_notes: '',
      status: 'مجدول',
    }
  })

  const watchedRequestId = watch('service_request_id')
  const watchedEngineerId = watch('engineer_id')
  const watchedDate = watch('scheduled_date')

  // Update selected request when request ID changes
  useEffect(() => {
    if (watchedRequestId) {
      const request = serviceRequests.find(r => r.id === watchedRequestId)
      setSelectedRequest(request || null)
    } else {
      setSelectedRequest(null)
    }
  }, [watchedRequestId, serviceRequests])

  // Update selected engineer when engineer ID changes
  useEffect(() => {
    if (watchedEngineerId) {
      const engineer = engineers.find(e => e.id === watchedEngineerId)
      setSelectedEngineer(engineer || null)
    } else {
      setSelectedEngineer(null)
    }
  }, [watchedEngineerId, engineers])

  const handleFormSubmit = async (data: SchedulingFormData) => {
    setIsSubmitting(true)
    try {
      await onSubmit(data)
      if (!initialData) {
        reset()
        setSelectedRequest(null)
        setSelectedEngineer(null)
      }
    } catch (error) {
      console.error('Error submitting scheduling form:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const getMinDateTime = () => {
    const now = new Date()
    now.setMinutes(now.getMinutes() + 30) // Minimum 30 minutes from now
    return now.toISOString().slice(0, 16)
  }

  const getPriorityColor = (priority: string) => {
    const colors: Record<string, string> = {
      'عاجل': 'bg-red-100 text-red-800',
      'متوسط': 'bg-yellow-100 text-yellow-800',
      'منخفض': 'bg-green-100 text-green-800',
    }
    return colors[priority] || 'bg-gray-100 text-gray-800'
  }

  const getStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      'قيد المراجعة': 'bg-yellow-100 text-yellow-800',
      'تم تسجيله': 'bg-blue-100 text-blue-800',
      'في انتظار فني': 'bg-orange-100 text-orange-800',
      'تم تحديد موعد': 'bg-purple-100 text-purple-800',
      'في الطريق للعميل': 'bg-indigo-100 text-indigo-800',
      'قيد التنفيذ': 'bg-green-100 text-green-800',
      'مكتمل': 'bg-green-100 text-green-800',
      'مغلق': 'bg-gray-100 text-gray-800',
    }
    return colors[status] || 'bg-gray-100 text-gray-800'
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-6 w-6 text-blue-600" />
          {initialData ? 'تعديل جدولة الصيانة' : 'جدولة موعد صيانة جديد'}
        </CardTitle>
        <CardDescription>
          {initialData 
            ? 'قم بتعديل بيانات الجدولة أدناه' 
            : 'حدد طلب الصيانة والمهندس والموعد المناسب'
          }
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {/* Service Request Selection */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">طلب الصيانة</h3>
            
            <div className="space-y-2">
              <Label>اختيار طلب الصيانة *</Label>
              <Controller
                name="service_request_id"
                control={control}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger className={errors.service_request_id ? 'border-red-500' : ''}>
                      <SelectValue placeholder="اختر طلب الصيانة" />
                    </SelectTrigger>
                    <SelectContent>
                      {serviceRequests.length === 0 ? (
                        <SelectItem value="no-requests" disabled>لا توجد طلبات صيانة متاحة</SelectItem>
                      ) : (
                        serviceRequests
                          .filter(request => !['مكتمل', 'مغلق'].includes(request.status))
                          .map((request) => (
                            <SelectItem key={request.id} value={request.id}>
                              <div className="flex flex-col">
                                <span className="font-medium line-clamp-1">{request.initial_description}</span>
                                <div className="flex items-center gap-2 mt-1">
                                  <span className="text-xs text-gray-500">{request.customer?.full_name}</span>
                                  <Badge className={getPriorityColor(request.priority)} variant="outline">
                                    {request.priority}
                                  </Badge>
                                </div>
                              </div>
                            </SelectItem>
                          ))
                      )}
                    </SelectContent>
                  </Select>
                )}
              />
              {errors.service_request_id && (
                <p className="text-sm text-red-500">{errors.service_request_id.message}</p>
              )}
            </div>

            {selectedRequest && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 mb-3">تفاصيل طلب الصيانة</h4>
                <div className="space-y-2 text-sm text-blue-700">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">العميل:</span>
                    <span>{selectedRequest.customer?.full_name}</span>
                    <span className="text-blue-500">({selectedRequest.customer?.primary_phone})</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="font-medium">الجهاز:</span>
                    <span>{selectedRequest.device?.device_name} - {selectedRequest.device?.model}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="font-medium">الوصف:</span>
                    <span className="line-clamp-2">{selectedRequest.initial_description}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="font-medium">الحالة:</span>
                    <Badge className={getStatusColor(selectedRequest.status)}>
                      {selectedRequest.status}
                    </Badge>
                    <Badge className={getPriorityColor(selectedRequest.priority)}>
                      {selectedRequest.priority}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="font-medium">تاريخ الطلب:</span>
                    <span>{formatDateTime(selectedRequest.request_date)}</span>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Engineer Selection */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">المهندس المكلف</h3>
            
            <div className="space-y-2">
              <Label>اختيار المهندس *</Label>
              <Controller
                name="engineer_id"
                control={control}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger className={errors.engineer_id ? 'border-red-500' : ''}>
                      <SelectValue placeholder="اختر المهندس" />
                    </SelectTrigger>
                    <SelectContent>
                      {engineers.length === 0 ? (
                        <SelectItem value="no-engineers" disabled>لا توجد مهندسين متاحين</SelectItem>
                      ) : (
                        engineers
                          .filter(engineer => engineer.is_active)
                          .map((engineer) => (
                            <SelectItem key={engineer.id} value={engineer.id}>
                              <div className="flex flex-col">
                                <span className="font-medium">{engineer.full_name}</span>
                                <div className="flex items-center gap-2 mt-1">
                                  <span className="text-xs text-gray-500">{engineer.phone}</span>
                                  {engineer.specialization && (
                                    <Badge variant="outline" className="text-xs">
                                      {engineer.specialization}
                                    </Badge>
                                  )}
                                </div>
                              </div>
                            </SelectItem>
                          ))
                      )}
                    </SelectContent>
                  </Select>
                )}
              />
              {errors.engineer_id && (
                <p className="text-sm text-red-500">{errors.engineer_id.message}</p>
              )}
            </div>

            {selectedEngineer && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h4 className="font-medium text-green-900 mb-3">معلومات المهندس</h4>
                <div className="space-y-2 text-sm text-green-700">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    <span className="font-medium">{selectedEngineer.full_name}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="font-medium">الهاتف:</span>
                    <span>{selectedEngineer.phone}</span>
                  </div>
                  {selectedEngineer.specialization && (
                    <div className="flex items-center gap-2">
                      <span className="font-medium">التخصص:</span>
                      <span>{selectedEngineer.specialization}</span>
                    </div>
                  )}
                  {selectedEngineer.email && (
                    <div className="flex items-center gap-2">
                      <span className="font-medium">البريد الإلكتروني:</span>
                      <span>{selectedEngineer.email}</span>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Scheduling Details */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">تفاصيل الموعد</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="scheduled_date">تاريخ ووقت الموعد *</Label>
                <Input
                  id="scheduled_date"
                  type="datetime-local"
                  {...register('scheduled_date')}
                  min={getMinDateTime()}
                  className={errors.scheduled_date ? 'border-red-500' : ''}
                />
                {errors.scheduled_date && (
                  <p className="text-sm text-red-500">{errors.scheduled_date.message}</p>
                )}
                <p className="text-sm text-gray-500">
                  يجب أن يكون الموعد بعد 30 دقيقة على الأقل من الآن
                </p>
              </div>

              <div className="space-y-2">
                <Label>المدة المقدرة *</Label>
                <Controller
                  name="estimated_duration"
                  control={control}
                  render={({ field }) => (
                    <Select
                      value={field.value.toString()}
                      onValueChange={(value) => field.onChange(parseInt(value))}
                    >
                      <SelectTrigger className={errors.estimated_duration ? 'border-red-500' : ''}>
                        <SelectValue placeholder="اختر المدة المقدرة" />
                      </SelectTrigger>
                      <SelectContent>
                        {DURATION_OPTIONS.map((option) => (
                          <SelectItem key={option.value} value={option.value.toString()}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
                {errors.estimated_duration && (
                  <p className="text-sm text-red-500">{errors.estimated_duration.message}</p>
                )}
              </div>
            </div>

            {initialData && (
              <div className="space-y-2">
                <Label>حالة الجدولة</Label>
                <Controller
                  name="status"
                  control={control}
                  render={({ field }) => (
                    <Select value={field.value} onValueChange={field.onChange}>
                      <SelectTrigger>
                        <SelectValue placeholder="اختر حالة الجدولة" />
                      </SelectTrigger>
                      <SelectContent>
                        {SCHEDULING_STATUS.map((status) => (
                          <SelectItem key={status.value} value={status.value}>
                            <div className="flex items-center gap-2">
                              <Badge className={status.color}>
                                {status.label}
                              </Badge>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="engineer_notes">ملاحظات للمهندس</Label>
              <Textarea
                id="engineer_notes"
                {...register('engineer_notes')}
                placeholder="أي ملاحظات أو تعليمات خاصة للمهندس"
                rows={3}
              />
              <p className="text-sm text-gray-500">
                اكتب أي معلومات إضافية قد تفيد المهندس قبل الزيارة
              </p>
            </div>
          </div>

          {/* Schedule Summary */}
          {watchedDate && selectedRequest && selectedEngineer && (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
                <Clock className="h-5 w-5" />
                ملخص الجدولة
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div className="space-y-2">
                  <div>
                    <span className="font-medium text-gray-700">الموعد:</span>
                    <p className="text-gray-600">{formatDateTime(watchedDate)}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">المدة المقدرة:</span>
                    <p className="text-gray-600">
                      {DURATION_OPTIONS.find(d => d.value === watch('estimated_duration'))?.label}
                    </p>
                  </div>
                </div>
                <div className="space-y-2">
                  <div>
                    <span className="font-medium text-gray-700">المهندس:</span>
                    <p className="text-gray-600">{selectedEngineer.full_name}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">العميل:</span>
                    <p className="text-gray-600">{selectedRequest.customer?.full_name}</p>
                  </div>
                </div>
              </div>

              {selectedRequest.priority === 'عاجل' && (
                <div className="mt-3 flex items-center gap-2 text-red-600">
                  <AlertCircle className="h-4 w-4" />
                  <span className="text-sm font-medium">هذا طلب عاجل - يُنصح بجدولته في أقرب وقت ممكن</span>
                </div>
              )}
            </div>
          )}

          {/* Form Actions */}
          <div className="flex gap-4 pt-6">
            <Button
              type="submit"
              disabled={isSubmitting || isLoading}
              className="flex-1"
            >
              <Save className="h-4 w-4 ml-2" />
              {isSubmitting ? 'جاري الحفظ...' : 'حفظ الجدولة'}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting || isLoading}
            >
              <X className="h-4 w-4 ml-2" />
              إلغاء
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
