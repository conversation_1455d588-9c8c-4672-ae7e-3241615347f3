'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Users, 
  ClipboardList, 
  Calendar, 
  Wrench, 
  Package,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Clock,
  DollarSign,
  Target,
  Activity
} from 'lucide-react'

interface DashboardStats {
  customers: {
    total: number
    companies: number
    individuals: number
    recentlyAdded: number
  }
  serviceRequests: {
    total: number
    pending: number
    inProgress: number
    completed: number
    urgent: number
  }
  scheduling: {
    total: number
    today: number
    thisWeek: number
    overdue: number
    completed: number
  }
  execution: {
    total: number
    completed: number
    inProgress: number
    averageDuration: number
  }
  parts: {
    total: number
    lowStock: number
    outOfStock: number
    totalValue: number
  }
  performance: {
    responseTime: number
    completionRate: number
    customerSatisfaction: number
    revenueThisMonth: number
  }
}

interface DashboardOverviewProps {
  isLoading?: boolean
}

export function DashboardOverview({ isLoading }: DashboardOverviewProps) {
  const [stats, setStats] = useState<DashboardStats>({
    customers: { total: 0, companies: 0, individuals: 0, recentlyAdded: 0 },
    serviceRequests: { total: 0, pending: 0, inProgress: 0, completed: 0, urgent: 0 },
    scheduling: { total: 0, today: 0, thisWeek: 0, overdue: 0, completed: 0 },
    execution: { total: 0, completed: 0, inProgress: 0, averageDuration: 0 },
    parts: { total: 0, lowStock: 0, outOfStock: 0, totalValue: 0 },
    performance: { responseTime: 0, completionRate: 0, customerSatisfaction: 0, revenueThisMonth: 0 }
  })

  useEffect(() => {
    loadDashboardStats()
  }, [])

  const loadDashboardStats = async () => {
    // في التطبيق الحقيقي سيتم جلب البيانات من APIs
    // هنا نستخدم بيانات تجريبية
    setTimeout(() => {
      setStats({
        customers: {
          total: 156,
          companies: 45,
          individuals: 111,
          recentlyAdded: 12
        },
        serviceRequests: {
          total: 324,
          pending: 23,
          inProgress: 18,
          completed: 283,
          urgent: 8
        },
        scheduling: {
          total: 298,
          today: 6,
          thisWeek: 24,
          overdue: 3,
          completed: 267
        },
        execution: {
          total: 267,
          completed: 251,
          inProgress: 16,
          averageDuration: 2.4
        },
        parts: {
          total: 89,
          lowStock: 12,
          outOfStock: 3,
          totalValue: 45670.50
        },
        performance: {
          responseTime: 4.2,
          completionRate: 94.5,
          customerSatisfaction: 4.7,
          revenueThisMonth: 125430.00
        }
      })
    }, 1000)
  }

  const getPerformanceColor = (value: number, threshold: number, reverse = false) => {
    const isGood = reverse ? value <= threshold : value >= threshold
    return isGood ? 'text-green-600' : 'text-red-600'
  }

  const getPerformanceIcon = (value: number, threshold: number, reverse = false) => {
    const isGood = reverse ? value <= threshold : value >= threshold
    return isGood ? TrendingUp : TrendingDown
  }

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(12)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="space-y-3">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Key Performance Indicators */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-6">مؤشرات الأداء الرئيسية</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">وقت الاستجابة</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${getPerformanceColor(stats.performance.responseTime, 6, true)}`}>
                {stats.performance.responseTime} ساعة
              </div>
              <p className="text-xs text-muted-foreground">
                متوسط وقت الاستجابة للطلبات
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">معدل الإكمال</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${getPerformanceColor(stats.performance.completionRate, 90)}`}>
                {stats.performance.completionRate}%
              </div>
              <p className="text-xs text-muted-foreground">
                نسبة الطلبات المكتملة بنجاح
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">رضا العملاء</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${getPerformanceColor(stats.performance.customerSatisfaction, 4.0)}`}>
                {stats.performance.customerSatisfaction}/5
              </div>
              <p className="text-xs text-muted-foreground">
                متوسط تقييم رضا العملاء
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">الإيرادات الشهرية</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {stats.performance.revenueThisMonth.toLocaleString()} ريال
              </div>
              <p className="text-xs text-muted-foreground">
                إجمالي الإيرادات هذا الشهر
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Main Statistics */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-6">الإحصائيات العامة</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Customers Stats */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">العملاء</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.customers.total}</div>
              <div className="flex items-center gap-2 mt-2">
                <Badge variant="outline">{stats.customers.companies} مؤسسة</Badge>
                <Badge variant="outline">{stats.customers.individuals} فرد</Badge>
              </div>
              <p className="text-xs text-muted-foreground mt-2">
                +{stats.customers.recentlyAdded} عميل جديد هذا الشهر
              </p>
            </CardContent>
          </Card>

          {/* Service Requests Stats */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">طلبات الصيانة</CardTitle>
              <ClipboardList className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.serviceRequests.total}</div>
              <div className="flex items-center gap-1 mt-2 flex-wrap">
                <Badge className="bg-yellow-100 text-yellow-800">
                  {stats.serviceRequests.pending} معلق
                </Badge>
                <Badge className="bg-blue-100 text-blue-800">
                  {stats.serviceRequests.inProgress} قيد التنفيذ
                </Badge>
              </div>
              <div className="flex items-center gap-2 mt-2">
                <AlertTriangle className="h-3 w-3 text-red-500" />
                <span className="text-xs text-red-600">
                  {stats.serviceRequests.urgent} طلب عاجل
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Scheduling Stats */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">الجدولة</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.scheduling.total}</div>
              <div className="flex items-center gap-1 mt-2 flex-wrap">
                <Badge className="bg-blue-100 text-blue-800">
                  {stats.scheduling.today} اليوم
                </Badge>
                <Badge className="bg-green-100 text-green-800">
                  {stats.scheduling.thisWeek} هذا الأسبوع
                </Badge>
              </div>
              {stats.scheduling.overdue > 0 && (
                <div className="flex items-center gap-2 mt-2">
                  <AlertTriangle className="h-3 w-3 text-red-500" />
                  <span className="text-xs text-red-600">
                    {stats.scheduling.overdue} موعد متأخر
                  </span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Execution Stats */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">التنفيذ</CardTitle>
              <Wrench className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.execution.total}</div>
              <div className="flex items-center gap-1 mt-2 flex-wrap">
                <Badge className="bg-green-100 text-green-800">
                  <CheckCircle className="h-3 w-3 ml-1" />
                  {stats.execution.completed} مكتمل
                </Badge>
                <Badge className="bg-blue-100 text-blue-800">
                  {stats.execution.inProgress} قيد العمل
                </Badge>
              </div>
              <p className="text-xs text-muted-foreground mt-2">
                متوسط المدة: {stats.execution.averageDuration} ساعة
              </p>
            </CardContent>
          </Card>

          {/* Parts Stats */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">قطع الغيار</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.parts.total}</div>
              <div className="flex items-center gap-1 mt-2 flex-wrap">
                {stats.parts.lowStock > 0 && (
                  <Badge className="bg-yellow-100 text-yellow-800">
                    {stats.parts.lowStock} مخزون منخفض
                  </Badge>
                )}
                {stats.parts.outOfStock > 0 && (
                  <Badge className="bg-red-100 text-red-800">
                    {stats.parts.outOfStock} نفد المخزون
                  </Badge>
                )}
              </div>
              <p className="text-xs text-muted-foreground mt-2">
                قيمة المخزون: {stats.parts.totalValue.toLocaleString()} ريال
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Alerts and Notifications */}
      {(stats.serviceRequests.urgent > 0 || stats.scheduling.overdue > 0 || stats.parts.outOfStock > 0) && (
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">التنبيهات المهمة</h2>
          <div className="space-y-4">
            {stats.serviceRequests.urgent > 0 && (
              <Card className="border-red-200 bg-red-50">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <AlertTriangle className="h-5 w-5 text-red-600" />
                    <div>
                      <h3 className="font-medium text-red-900">
                        طلبات صيانة عاجلة
                      </h3>
                      <p className="text-sm text-red-700">
                        يوجد {stats.serviceRequests.urgent} طلب صيانة عاجل يحتاج اهتمام فوري
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {stats.scheduling.overdue > 0 && (
              <Card className="border-orange-200 bg-orange-50">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <Clock className="h-5 w-5 text-orange-600" />
                    <div>
                      <h3 className="font-medium text-orange-900">
                        مواعيد متأخرة
                      </h3>
                      <p className="text-sm text-orange-700">
                        يوجد {stats.scheduling.overdue} موعد متأخر يحتاج إعادة جدولة
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {stats.parts.outOfStock > 0 && (
              <Card className="border-red-200 bg-red-50">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <Package className="h-5 w-5 text-red-600" />
                    <div>
                      <h3 className="font-medium text-red-900">
                        قطع غيار نفد مخزونها
                      </h3>
                      <p className="text-sm text-red-700">
                        يوجد {stats.parts.outOfStock} قطعة غيار نفد مخزونها ويجب إعادة التموين
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
