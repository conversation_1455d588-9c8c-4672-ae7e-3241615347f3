'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { CustomerForm } from '@/components/customers/customer-form'
import { Button } from '@/components/ui/button'
import { ArrowRight, Users } from 'lucide-react'
import Link from 'next/link'

export default function NewCustomerPage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (data: any) => {
    setIsLoading(true)
    try {
      // في التطبيق الحقيقي سيتم إرسال البيانات إلى Supabase
      console.log('Creating new customer:', data)
      
      // محاكاة API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // إظهار رسالة نجاح
      alert('تم إضافة العميل بنجاح!')
      
      // العودة لصفحة العملاء
      router.push('/customers')
    } catch (error) {
      console.error('Error creating customer:', error)
      alert('حدث خطأ أثناء إضافة العميل')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    router.push('/customers')
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center gap-4">
              <Link href="/customers">
                <Button variant="ghost" className="flex items-center gap-2">
                  <ArrowRight className="h-4 w-4" />
                  العودة للعملاء
                </Button>
              </Link>
              <div className="flex items-center gap-3">
                <Users className="h-8 w-8 text-blue-600" />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    إضافة عميل جديد
                  </h1>
                  <p className="text-gray-600">
                    أدخل بيانات العميل الجديد في النموذج أدناه
                  </p>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <Link href="/">
                <Button variant="outline">
                  العودة للرئيسية
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <CustomerForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isLoading={isLoading}
        />
      </div>
    </div>
  )
}
