import { supabase } from '@/lib/supabase'
import { Device, DeviceFormData, ApiResponse } from '@/types'

export class DevicesAPI {
  // Get all devices
  static async getAll(): Promise<ApiResponse<Device[]>> {
    try {
      const { data, error } = await supabase
        .from('devices')
        .select(`
          *,
          customers(full_name, primary_phone)
        `)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching devices:', error)
        return { error: 'فشل في جلب بيانات الأجهزة' }
      }

      return { data: data || [] }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Get devices by customer ID
  static async getByCustomerId(customerId: string): Promise<ApiResponse<Device[]>> {
    try {
      const { data, error } = await supabase
        .from('devices')
        .select('*')
        .eq('customer_id', customerId)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching customer devices:', error)
        return { error: 'فشل في جلب أجهزة العميل' }
      }

      return { data: data || [] }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Get device by ID
  static async getById(id: string): Promise<ApiResponse<Device>> {
    try {
      const { data, error } = await supabase
        .from('devices')
        .select(`
          *,
          customers(full_name, primary_phone, detailed_address)
        `)
        .eq('id', id)
        .single()

      if (error) {
        console.error('Error fetching device:', error)
        return { error: 'فشل في جلب بيانات الجهاز' }
      }

      if (!data) {
        return { error: 'الجهاز غير موجود' }
      }

      return { data }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Create new device
  static async create(deviceData: DeviceFormData & { customer_id: string }): Promise<ApiResponse<Device>> {
    try {
      // Check if serial number already exists
      const { data: existingDevice } = await supabase
        .from('devices')
        .select('id')
        .eq('serial_number', deviceData.serial_number)
        .single()

      if (existingDevice) {
        return { error: 'الرقم التسلسلي مسجل مسبقاً لجهاز آخر' }
      }

      // Verify customer exists
      const { data: customer } = await supabase
        .from('customers')
        .select('id')
        .eq('id', deviceData.customer_id)
        .single()

      if (!customer) {
        return { error: 'العميل غير موجود' }
      }

      const { data, error } = await supabase
        .from('devices')
        .insert([deviceData])
        .select()
        .single()

      if (error) {
        console.error('Error creating device:', error)
        return { error: 'فشل في إنشاء الجهاز' }
      }

      return { 
        data, 
        message: 'تم إنشاء الجهاز بنجاح' 
      }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Update device
  static async update(id: string, deviceData: Partial<DeviceFormData>): Promise<ApiResponse<Device>> {
    try {
      // If updating serial number, check if it's already used by another device
      if (deviceData.serial_number) {
        const { data: existingDevice } = await supabase
          .from('devices')
          .select('id')
          .eq('serial_number', deviceData.serial_number)
          .neq('id', id)
          .single()

        if (existingDevice) {
          return { error: 'الرقم التسلسلي مسجل مسبقاً لجهاز آخر' }
        }
      }

      const { data, error } = await supabase
        .from('devices')
        .update(deviceData)
        .eq('id', id)
        .select()
        .single()

      if (error) {
        console.error('Error updating device:', error)
        return { error: 'فشل في تحديث بيانات الجهاز' }
      }

      return { 
        data, 
        message: 'تم تحديث بيانات الجهاز بنجاح' 
      }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Delete device
  static async delete(id: string): Promise<ApiResponse<void>> {
    try {
      // Check if device has any service requests
      const { data: serviceRequests } = await supabase
        .from('service_requests')
        .select('id')
        .eq('device_id', id)
        .limit(1)

      if (serviceRequests && serviceRequests.length > 0) {
        return { error: 'لا يمكن حذف الجهاز لأنه يحتوي على طلبات صيانة' }
      }

      const { error } = await supabase
        .from('devices')
        .delete()
        .eq('id', id)

      if (error) {
        console.error('Error deleting device:', error)
        return { error: 'فشل في حذف الجهاز' }
      }

      return { message: 'تم حذف الجهاز بنجاح' }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Search devices
  static async search(query: string): Promise<ApiResponse<Device[]>> {
    try {
      const { data, error } = await supabase
        .from('devices')
        .select(`
          *,
          customers(full_name, primary_phone)
        `)
        .or(`device_name.ilike.%${query}%,model.ilike.%${query}%,serial_number.ilike.%${query}%`)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error searching devices:', error)
        return { error: 'فشل في البحث عن الأجهزة' }
      }

      return { data: data || [] }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Get device statistics
  static async getStatistics(): Promise<ApiResponse<{
    total: number
    underWarranty: number
    expiredWarranty: number
    noWarrantyInfo: number
  }>> {
    try {
      const { data: allDevices, error } = await supabase
        .from('devices')
        .select('warranty_end_date')

      if (error) {
        console.error('Error fetching device statistics:', error)
        return { error: 'فشل في جلب إحصائيات الأجهزة' }
      }

      const devices = allDevices || []
      const today = new Date()
      
      let underWarranty = 0
      let expiredWarranty = 0
      let noWarrantyInfo = 0

      devices.forEach(device => {
        if (!device.warranty_end_date) {
          noWarrantyInfo++
        } else {
          const warrantyEndDate = new Date(device.warranty_end_date)
          if (warrantyEndDate > today) {
            underWarranty++
          } else {
            expiredWarranty++
          }
        }
      })

      return {
        data: {
          total: devices.length,
          underWarranty,
          expiredWarranty,
          noWarrantyInfo
        }
      }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Get devices with warranty status
  static async getWithWarrantyStatus(): Promise<ApiResponse<(Device & { warranty_status: 'active' | 'expired' | 'unknown' })[]>> {
    try {
      const { data, error } = await supabase
        .from('devices')
        .select(`
          *,
          customers(full_name, primary_phone)
        `)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching devices with warranty status:', error)
        return { error: 'فشل في جلب بيانات الأجهزة' }
      }

      const today = new Date()
      const devicesWithStatus = (data || []).map(device => {
        let warranty_status: 'active' | 'expired' | 'unknown' = 'unknown'
        
        if (device.warranty_end_date) {
          const warrantyEndDate = new Date(device.warranty_end_date)
          warranty_status = warrantyEndDate > today ? 'active' : 'expired'
        }

        return {
          ...device,
          warranty_status
        }
      })

      return { data: devicesWithStatus }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Get devices expiring soon (within next 30 days)
  static async getExpiringSoon(): Promise<ApiResponse<Device[]>> {
    try {
      const today = new Date()
      const thirtyDaysFromNow = new Date()
      thirtyDaysFromNow.setDate(today.getDate() + 30)

      const { data, error } = await supabase
        .from('devices')
        .select(`
          *,
          customers(full_name, primary_phone)
        `)
        .gte('warranty_end_date', today.toISOString().split('T')[0])
        .lte('warranty_end_date', thirtyDaysFromNow.toISOString().split('T')[0])
        .order('warranty_end_date', { ascending: true })

      if (error) {
        console.error('Error fetching devices expiring soon:', error)
        return { error: 'فشل في جلب الأجهزة منتهية الضمان قريباً' }
      }

      return { data: data || [] }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }
}
