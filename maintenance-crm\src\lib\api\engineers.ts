import { supabase } from '@/lib/supabase'
import { Engineer, ApiResponse } from '@/types'

interface EngineerFormData {
  full_name: string
  phone: string
  email?: string
  specialization?: string
  is_active?: boolean
}

export class EngineersAPI {
  // Get all engineers
  static async getAll(): Promise<ApiResponse<Engineer[]>> {
    try {
      const { data, error } = await supabase
        .from('engineers')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching engineers:', error)
        return { error: 'فشل في جلب بيانات المهندسين' }
      }

      return { data: data || [] }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Get active engineers only
  static async getActive(): Promise<ApiResponse<Engineer[]>> {
    try {
      const { data, error } = await supabase
        .from('engineers')
        .select('*')
        .eq('is_active', true)
        .order('full_name', { ascending: true })

      if (error) {
        console.error('Error fetching active engineers:', error)
        return { error: 'فشل في جلب بيانات المهندسين النشطين' }
      }

      return { data: data || [] }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Get engineer by ID
  static async getById(id: string): Promise<ApiResponse<Engineer>> {
    try {
      const { data, error } = await supabase
        .from('engineers')
        .select('*')
        .eq('id', id)
        .single()

      if (error) {
        console.error('Error fetching engineer:', error)
        return { error: 'فشل في جلب بيانات المهندس' }
      }

      if (!data) {
        return { error: 'المهندس غير موجود' }
      }

      return { data }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Create new engineer
  static async create(engineerData: EngineerFormData): Promise<ApiResponse<Engineer>> {
    try {
      // Check if phone number already exists
      const { data: existingEngineer } = await supabase
        .from('engineers')
        .select('id')
        .eq('phone', engineerData.phone)
        .single()

      if (existingEngineer) {
        return { error: 'رقم الهاتف مسجل مسبقاً لمهندس آخر' }
      }

      // Check if email already exists (if provided)
      if (engineerData.email) {
        const { data: existingEmail } = await supabase
          .from('engineers')
          .select('id')
          .eq('email', engineerData.email)
          .single()

        if (existingEmail) {
          return { error: 'البريد الإلكتروني مسجل مسبقاً لمهندس آخر' }
        }
      }

      const { data, error } = await supabase
        .from('engineers')
        .insert([{
          ...engineerData,
          is_active: engineerData.is_active !== undefined ? engineerData.is_active : true
        }])
        .select()
        .single()

      if (error) {
        console.error('Error creating engineer:', error)
        return { error: 'فشل في إنشاء المهندس' }
      }

      return { 
        data, 
        message: 'تم إنشاء المهندس بنجاح' 
      }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Update engineer
  static async update(id: string, engineerData: Partial<EngineerFormData>): Promise<ApiResponse<Engineer>> {
    try {
      // If updating phone number, check if it's already used by another engineer
      if (engineerData.phone) {
        const { data: existingEngineer } = await supabase
          .from('engineers')
          .select('id')
          .eq('phone', engineerData.phone)
          .neq('id', id)
          .single()

        if (existingEngineer) {
          return { error: 'رقم الهاتف مسجل مسبقاً لمهندس آخر' }
        }
      }

      // If updating email, check if it's already used by another engineer
      if (engineerData.email) {
        const { data: existingEmail } = await supabase
          .from('engineers')
          .select('id')
          .eq('email', engineerData.email)
          .neq('id', id)
          .single()

        if (existingEmail) {
          return { error: 'البريد الإلكتروني مسجل مسبقاً لمهندس آخر' }
        }
      }

      const { data, error } = await supabase
        .from('engineers')
        .update(engineerData)
        .eq('id', id)
        .select()
        .single()

      if (error) {
        console.error('Error updating engineer:', error)
        return { error: 'فشل في تحديث بيانات المهندس' }
      }

      return { 
        data, 
        message: 'تم تحديث بيانات المهندس بنجاح' 
      }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Toggle engineer active status
  static async toggleActiveStatus(id: string): Promise<ApiResponse<Engineer>> {
    try {
      // Get current status
      const currentEngineer = await this.getById(id)
      if (currentEngineer.error || !currentEngineer.data) {
        return { error: 'المهندس غير موجود' }
      }

      const newStatus = !currentEngineer.data.is_active

      // If deactivating, check if engineer has active schedulings
      if (!newStatus) {
        const { data: activeSchedulings } = await supabase
          .from('scheduling')
          .select('id')
          .eq('engineer_id', id)
          .gte('scheduled_date', new Date().toISOString())
          .not('status', 'in', '(مكتمل,ملغي)')
          .limit(1)

        if (activeSchedulings && activeSchedulings.length > 0) {
          return { error: 'لا يمكن إلغاء تفعيل المهندس لأنه لديه جدولات نشطة' }
        }
      }

      const { data, error } = await supabase
        .from('engineers')
        .update({ is_active: newStatus })
        .eq('id', id)
        .select()
        .single()

      if (error) {
        console.error('Error toggling engineer status:', error)
        return { error: 'فشل في تغيير حالة المهندس' }
      }

      return { 
        data, 
        message: newStatus ? 'تم تفعيل المهندس بنجاح' : 'تم إلغاء تفعيل المهندس بنجاح'
      }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Delete engineer
  static async delete(id: string): Promise<ApiResponse<void>> {
    try {
      // Check if engineer has any schedulings
      const { data: schedulings } = await supabase
        .from('scheduling')
        .select('id')
        .eq('engineer_id', id)
        .limit(1)

      if (schedulings && schedulings.length > 0) {
        return { error: 'لا يمكن حذف المهندس لأنه يحتوي على جدولات' }
      }

      // Check if engineer has any executions
      const { data: executions } = await supabase
        .from('execution')
        .select('id')
        .eq('engineer_id', id)
        .limit(1)

      if (executions && executions.length > 0) {
        return { error: 'لا يمكن حذف المهندس لأنه يحتوي على سجلات تنفيذ' }
      }

      const { error } = await supabase
        .from('engineers')
        .delete()
        .eq('id', id)

      if (error) {
        console.error('Error deleting engineer:', error)
        return { error: 'فشل في حذف المهندس' }
      }

      return { message: 'تم حذف المهندس بنجاح' }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Search engineers
  static async search(query: string): Promise<ApiResponse<Engineer[]>> {
    try {
      const { data, error } = await supabase
        .from('engineers')
        .select('*')
        .or(`full_name.ilike.%${query}%,phone.like.%${query}%,email.ilike.%${query}%,specialization.ilike.%${query}%`)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error searching engineers:', error)
        return { error: 'فشل في البحث عن المهندسين' }
      }

      return { data: data || [] }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Get engineer statistics
  static async getStatistics(): Promise<ApiResponse<{
    total: number
    active: number
    inactive: number
    specializations: Record<string, number>
  }>> {
    try {
      const { data: allEngineers, error } = await supabase
        .from('engineers')
        .select('is_active, specialization')

      if (error) {
        console.error('Error fetching engineer statistics:', error)
        return { error: 'فشل في جلب إحصائيات المهندسين' }
      }

      const engineers = allEngineers || []
      
      const active = engineers.filter(e => e.is_active).length
      const inactive = engineers.length - active

      // Count by specialization
      const specializations = engineers.reduce((acc, engineer) => {
        if (engineer.specialization) {
          acc[engineer.specialization] = (acc[engineer.specialization] || 0) + 1
        }
        return acc
      }, {} as Record<string, number>)

      return {
        data: {
          total: engineers.length,
          active,
          inactive,
          specializations
        }
      }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Get engineer workload (number of active schedulings)
  static async getWorkload(engineerId: string): Promise<ApiResponse<{
    totalSchedulings: number
    todaySchedulings: number
    weekSchedulings: number
    overdueSchedulings: number
  }>> {
    try {
      const now = new Date()
      const today = new Date(now.setHours(0, 0, 0, 0)).toISOString()
      const endOfToday = new Date(now.setHours(23, 59, 59, 999)).toISOString()
      const weekFromNow = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()

      // Get all active schedulings for engineer
      const { data: allSchedulings, error: allError } = await supabase
        .from('scheduling')
        .select('scheduled_date, status')
        .eq('engineer_id', engineerId)
        .not('status', 'in', '(مكتمل,ملغي)')

      if (allError) {
        console.error('Error fetching engineer workload:', allError)
        return { error: 'فشل في جلب عبء عمل المهندس' }
      }

      const schedulings = allSchedulings || []
      
      const todaySchedulings = schedulings.filter(s => 
        s.scheduled_date >= today && s.scheduled_date <= endOfToday
      ).length

      const weekSchedulings = schedulings.filter(s => 
        s.scheduled_date >= today && s.scheduled_date <= weekFromNow
      ).length

      const overdueSchedulings = schedulings.filter(s => 
        new Date(s.scheduled_date) < new Date(today)
      ).length

      return {
        data: {
          totalSchedulings: schedulings.length,
          todaySchedulings,
          weekSchedulings,
          overdueSchedulings
        }
      }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Get engineers by specialization
  static async getBySpecialization(specialization: string): Promise<ApiResponse<Engineer[]>> {
    try {
      const { data, error } = await supabase
        .from('engineers')
        .select('*')
        .eq('specialization', specialization)
        .eq('is_active', true)
        .order('full_name', { ascending: true })

      if (error) {
        console.error('Error fetching engineers by specialization:', error)
        return { error: 'فشل في جلب المهندسين حسب التخصص' }
      }

      return { data: data || [] }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }
}
