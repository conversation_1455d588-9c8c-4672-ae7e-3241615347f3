'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Scheduling, Part, ExecutionPart } from '@/types'
import { Save, X, Wrench, Clock, Plus, Minus, AlertCircle } from 'lucide-react'
import { formatDateTime, formatTime } from '@/lib/utils'

const executionSchema = z.object({
  service_request_id: z.string().min(1, 'يج<PERSON> اختيار طلب الصيانة'),
  engineer_id: z.string().min(1, 'يجب اختيار المهندس'),
  arrival_time: z.string().optional(),
  start_time: z.string().optional(),
  end_time: z.string().optional(),
  technical_description: z.string().min(10, 'الوصف الفني مطلوب (حد أدنى 10 أحرف)'),
  repair_actions: z.string().min(10, 'الإجراءات المتخذة مطلوبة (حد أدنى 10 أحرف)'),
  failure_cause: z.string().optional(),
  tools_used: z.string().optional(),
  future_recommendations: z.string().optional(),
  received_by_customer: z.string().optional(),
  device_status_after: z.string().min(1, 'حالة الجهاز بعد الإصلاح مطلوبة'),
  customer_signature: z.string().optional(),
  technical_report: z.string().optional(),
})

interface ExecutionFormData {
  service_request_id: string
  engineer_id: string
  arrival_time?: string
  start_time?: string
  end_time?: string
  technical_description: string
  repair_actions: string
  failure_cause?: string
  tools_used?: string
  future_recommendations?: string
  received_by_customer?: string
  device_status_after: string
  customer_signature?: string
  technical_report?: string
}

interface ExecutionFormProps {
  initialData?: Partial<ExecutionFormData>
  scheduling?: Scheduling
  availableParts?: Part[]
  onSubmit: (data: ExecutionFormData, usedParts: ExecutionPart[]) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}

const DEVICE_STATUS_OPTIONS = [
  'يعمل بكفاءة عالية',
  'يعمل بشكل طبيعي',
  'يعمل مع ملاحظات',
  'يحتاج متابعة',
  'غير قابل للإصلاح',
  'تم استبداله'
]

const COMMON_TOOLS = [
  'مفكات متنوعة',
  'مفاتيح ربط',
  'جهاز قياس كهربائي',
  'مضخة تفريغ',
  'جهاز كشف التسريب',
  'أدوات تنظيف',
  'مواد عزل',
  'أدوات لحام'
]

export function ExecutionForm({ 
  initialData, 
  scheduling,
  availableParts = [],
  onSubmit, 
  onCancel, 
  isLoading 
}: ExecutionFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [usedParts, setUsedParts] = useState<ExecutionPart[]>([])
  const [selectedTools, setSelectedTools] = useState<string[]>([])

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    reset,
    watch,
    setValue
  } = useForm<ExecutionFormData>({
    resolver: zodResolver(executionSchema),
    defaultValues: initialData || {
      service_request_id: scheduling?.service_request_id || '',
      engineer_id: scheduling?.engineer_id || '',
      arrival_time: '',
      start_time: '',
      end_time: '',
      technical_description: '',
      repair_actions: '',
      failure_cause: '',
      tools_used: '',
      future_recommendations: '',
      received_by_customer: '',
      device_status_after: '',
      customer_signature: '',
      technical_report: '',
    }
  })

  const watchedStartTime = watch('start_time')
  const watchedEndTime = watch('end_time')

  // Auto-fill current time functions
  const setCurrentTime = (field: 'arrival_time' | 'start_time' | 'end_time') => {
    const now = new Date()
    const timeString = now.toISOString().slice(0, 16)
    setValue(field, timeString)
  }

  // Calculate work duration
  const calculateDuration = () => {
    if (watchedStartTime && watchedEndTime) {
      const start = new Date(watchedStartTime)
      const end = new Date(watchedEndTime)
      const diffMs = end.getTime() - start.getTime()
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
      const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60))
      
      if (diffMs > 0) {
        return `${diffHours} ساعة و ${diffMinutes} دقيقة`
      }
    }
    return null
  }

  // Add used part
  const addUsedPart = () => {
    setUsedParts([...usedParts, {
      id: '',
      execution_id: '',
      part_id: '',
      quantity_used: 1,
      unit_price_at_time: 0,
      created_at: '',
      part: undefined
    }])
  }

  // Remove used part
  const removeUsedPart = (index: number) => {
    setUsedParts(usedParts.filter((_, i) => i !== index))
  }

  // Update used part
  const updateUsedPart = (index: number, field: keyof ExecutionPart, value: any) => {
    const updated = [...usedParts]
    updated[index] = { ...updated[index], [field]: value }
    
    // If part is selected, update price
    if (field === 'part_id') {
      const selectedPart = availableParts.find(p => p.id === value)
      if (selectedPart) {
        updated[index].unit_price_at_time = selectedPart.unit_price
        updated[index].part = selectedPart
      }
    }
    
    setUsedParts(updated)
  }

  // Toggle tool selection
  const toggleTool = (tool: string) => {
    const updated = selectedTools.includes(tool)
      ? selectedTools.filter(t => t !== tool)
      : [...selectedTools, tool]
    setSelectedTools(updated)
    setValue('tools_used', updated.join(', '))
  }

  const handleFormSubmit = async (data: ExecutionFormData) => {
    setIsSubmitting(true)
    try {
      await onSubmit(data, usedParts)
      if (!initialData) {
        reset()
        setUsedParts([])
        setSelectedTools([])
      }
    } catch (error) {
      console.error('Error submitting execution form:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const getTotalPartsCost = () => {
    return usedParts.reduce((total, part) => 
      total + (part.quantity_used * part.unit_price_at_time), 0
    )
  }

  return (
    <div className="space-y-6">
      {/* Scheduling Info */}
      {scheduling && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-blue-600" />
              معلومات الجدولة
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700">الموعد المجدول:</span>
                <p className="text-gray-600">{formatDateTime(scheduling.scheduled_date)}</p>
              </div>
              <div>
                <span className="font-medium text-gray-700">المدة المقدرة:</span>
                <p className="text-gray-600">{scheduling.estimated_duration} دقيقة</p>
              </div>
              <div>
                <span className="font-medium text-gray-700">حالة الجدولة:</span>
                <Badge className="mr-2">{scheduling.status}</Badge>
              </div>
            </div>
            {scheduling.service_request && (
              <div className="mt-4 pt-4 border-t">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">العميل:</span>
                    <p className="text-gray-600">{scheduling.service_request.customer?.full_name}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">الجهاز:</span>
                    <p className="text-gray-600">
                      {scheduling.service_request.device?.device_name} - {scheduling.service_request.device?.model}
                    </p>
                  </div>
                </div>
                <div className="mt-2">
                  <span className="font-medium text-gray-700">وصف العطل:</span>
                  <p className="text-gray-600">{scheduling.service_request.initial_description}</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Main Form */}
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wrench className="h-6 w-6 text-blue-600" />
            {initialData ? 'تعديل تقرير التنفيذ' : 'تسجيل تنفيذ الصيانة'}
          </CardTitle>
          <CardDescription>
            {initialData 
              ? 'قم بتعديل تفاصيل تنفيذ الصيانة أدناه' 
              : 'سجل تفاصيل العمل المنجز والإجراءات المتخذة'
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
            {/* Time Tracking */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">تتبع الأوقات</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="arrival_time">وقت الوصول</Label>
                  <div className="flex gap-2">
                    <Input
                      id="arrival_time"
                      type="datetime-local"
                      {...register('arrival_time')}
                      className="flex-1"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentTime('arrival_time')}
                    >
                      الآن
                    </Button>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="start_time">وقت بدء العمل</Label>
                  <div className="flex gap-2">
                    <Input
                      id="start_time"
                      type="datetime-local"
                      {...register('start_time')}
                      className="flex-1"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentTime('start_time')}
                    >
                      الآن
                    </Button>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="end_time">وقت انتهاء العمل</Label>
                  <div className="flex gap-2">
                    <Input
                      id="end_time"
                      type="datetime-local"
                      {...register('end_time')}
                      className="flex-1"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentTime('end_time')}
                    >
                      الآن
                    </Button>
                  </div>
                </div>
              </div>

              {calculateDuration() && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                  <p className="text-blue-800">
                    <span className="font-medium">مدة العمل:</span> {calculateDuration()}
                  </p>
                </div>
              )}
            </div>

            {/* Technical Details */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">التفاصيل الفنية</h3>
              
              <div className="space-y-2">
                <Label htmlFor="technical_description">الوصف الفني للمشكلة *</Label>
                <Textarea
                  id="technical_description"
                  {...register('technical_description')}
                  placeholder="اكتب وصفاً فنياً دقيقاً للمشكلة التي تم اكتشافها"
                  className={errors.technical_description ? 'border-red-500' : ''}
                  rows={3}
                />
                {errors.technical_description && (
                  <p className="text-sm text-red-500">{errors.technical_description.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="repair_actions">الإجراءات المتخذة *</Label>
                <Textarea
                  id="repair_actions"
                  {...register('repair_actions')}
                  placeholder="اكتب تفصيلاً للإجراءات والإصلاحات التي تم تنفيذها"
                  className={errors.repair_actions ? 'border-red-500' : ''}
                  rows={3}
                />
                {errors.repair_actions && (
                  <p className="text-sm text-red-500">{errors.repair_actions.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="failure_cause">سبب العطل</Label>
                <Textarea
                  id="failure_cause"
                  {...register('failure_cause')}
                  placeholder="اكتب السبب الجذري للعطل إن أمكن تحديده"
                  rows={2}
                />
              </div>
            </div>

            {/* Tools Used */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">الأدوات المستخدمة</h3>

              <div className="space-y-3">
                <Label>الأدوات الشائعة</Label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                  {COMMON_TOOLS.map((tool) => (
                    <Button
                      key={tool}
                      type="button"
                      variant={selectedTools.includes(tool) ? "default" : "outline"}
                      size="sm"
                      onClick={() => toggleTool(tool)}
                      className="justify-start"
                    >
                      {tool}
                    </Button>
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="tools_used">أدوات أخرى</Label>
                <Textarea
                  id="tools_used"
                  {...register('tools_used')}
                  placeholder="اكتب أي أدوات أخرى تم استخدامها"
                  rows={2}
                />
              </div>
            </div>

            {/* Parts Used */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">قطع الغيار المستخدمة</h3>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addUsedPart}
                >
                  <Plus className="h-4 w-4 ml-2" />
                  إضافة قطعة
                </Button>
              </div>

              {usedParts.length === 0 ? (
                <div className="text-center py-8 bg-gray-50 rounded-lg">
                  <p className="text-gray-500">لم يتم استخدام قطع غيار</p>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addUsedPart}
                    className="mt-2"
                  >
                    <Plus className="h-4 w-4 ml-2" />
                    إضافة قطعة غيار
                  </Button>
                </div>
              ) : (
                <div className="space-y-3">
                  {usedParts.map((usedPart, index) => (
                    <div key={index} className="border rounded-lg p-4 bg-gray-50">
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div className="space-y-2">
                          <Label>قطعة الغيار</Label>
                          <Select
                            value={usedPart.part_id}
                            onValueChange={(value) => updateUsedPart(index, 'part_id', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="اختر قطعة الغيار" />
                            </SelectTrigger>
                            <SelectContent>
                              {availableParts.map((part) => (
                                <SelectItem key={part.id} value={part.id}>
                                  <div className="flex flex-col">
                                    <span className="font-medium">{part.part_name}</span>
                                    <span className="text-sm text-gray-500">
                                      {part.part_number} - {part.unit_price} ريال
                                    </span>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label>الكمية</Label>
                          <Input
                            type="number"
                            min="1"
                            value={usedPart.quantity_used}
                            onChange={(e) => updateUsedPart(index, 'quantity_used', parseInt(e.target.value) || 1)}
                          />
                        </div>

                        <div className="space-y-2">
                          <Label>السعر</Label>
                          <Input
                            type="number"
                            step="0.01"
                            value={usedPart.unit_price_at_time}
                            onChange={(e) => updateUsedPart(index, 'unit_price_at_time', parseFloat(e.target.value) || 0)}
                          />
                        </div>

                        <div className="flex items-end">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => removeUsedPart(index)}
                            className="w-full"
                          >
                            <Minus className="h-4 w-4 ml-2" />
                            حذف
                          </Button>
                        </div>
                      </div>

                      {usedPart.part && (
                        <div className="mt-3 text-sm text-gray-600">
                          <p><span className="font-medium">الإجمالي:</span> {(usedPart.quantity_used * usedPart.unit_price_at_time).toFixed(2)} ريال</p>
                          {usedPart.part.stock_quantity < usedPart.quantity_used && (
                            <div className="flex items-center gap-2 text-red-600 mt-1">
                              <AlertCircle className="h-4 w-4" />
                              <span>الكمية المطلوبة أكبر من المتوفر في المخزون ({usedPart.part.stock_quantity})</span>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  ))}

                  {usedParts.length > 0 && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                      <p className="text-blue-800">
                        <span className="font-medium">إجمالي تكلفة قطع الغيار:</span> {getTotalPartsCost().toFixed(2)} ريال
                      </p>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Final Status and Recommendations */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">الحالة النهائية والتوصيات</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>حالة الجهاز بعد الإصلاح *</Label>
                  <Controller
                    name="device_status_after"
                    control={control}
                    render={({ field }) => (
                      <Select value={field.value} onValueChange={field.onChange}>
                        <SelectTrigger className={errors.device_status_after ? 'border-red-500' : ''}>
                          <SelectValue placeholder="اختر حالة الجهاز" />
                        </SelectTrigger>
                        <SelectContent>
                          {DEVICE_STATUS_OPTIONS.map((status) => (
                            <SelectItem key={status} value={status}>
                              {status}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    )}
                  />
                  {errors.device_status_after && (
                    <p className="text-sm text-red-500">{errors.device_status_after.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="received_by_customer">استلم العمل</Label>
                  <Input
                    id="received_by_customer"
                    {...register('received_by_customer')}
                    placeholder="اسم الشخص الذي استلم العمل"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="future_recommendations">التوصيات المستقبلية</Label>
                <Textarea
                  id="future_recommendations"
                  {...register('future_recommendations')}
                  placeholder="اكتب أي توصيات للصيانة الدورية أو تحسينات مقترحة"
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="technical_report">التقرير الفني الإضافي</Label>
                <Textarea
                  id="technical_report"
                  {...register('technical_report')}
                  placeholder="أي ملاحظات فنية إضافية أو تفاصيل مهمة"
                  rows={3}
                />
              </div>
            </div>

            {/* Customer Signature */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">توقيع العميل</h3>

              <div className="space-y-2">
                <Label htmlFor="customer_signature">توقيع العميل (اختياري)</Label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                  <p className="text-gray-500">سيتم إضافة ميزة التوقيع الرقمي قريباً</p>
                  <Input
                    id="customer_signature"
                    {...register('customer_signature')}
                    placeholder="أو اكتب اسم الموقع"
                    className="mt-4 max-w-md mx-auto"
                  />
                </div>
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex gap-4 pt-6">
              <Button
                type="submit"
                disabled={isSubmitting || isLoading}
                className="flex-1"
              >
                <Save className="h-4 w-4 ml-2" />
                {isSubmitting ? 'جاري الحفظ...' : 'حفظ التقرير'}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting || isLoading}
              >
                <X className="h-4 w-4 ml-2" />
                إلغاء
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
