# 🚀 دليل البدء السريع

## خطوات التشغيل في 5 دقائق

### 1. التحقق من المتطلبات ✅

```bash
# تشغيل فحص النظام
node check-system.js
```

### 2. إعداد قاعدة البيانات 🗄️

1. **إنشاء حساب Supabase:**
   - اذهب إلى [supabase.com](https://supabase.com)
   - أنشئ حساب مجاني
   - أنشئ مشروع جديد

2. **نسخ بيانات الاتصال:**
   ```bash
   # انسخ .env.example إلى .env.local
   cp .env.example .env.local
   ```
   
3. **تعديل .env.local:**
   ```env
   NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
   ```

4. **تشغيل SQL Scripts:**
   - في Supabase > SQL Editor
   - نفذ `database/schema.sql`
   - نفذ `database/rls-policies.sql`
   - نفذ `database/seed.sql`

### 3. تشغيل النظام 🎯

**Windows:**
```bash
start.bat
```

**Mac/Linux:**
```bash
./start.sh
```

**أو يدوياً:**
```bash
npm install
npm run dev
```

### 4. الوصول للنظام 🌐

افتح المتصفح: **http://localhost:3000**

---

## 📋 الوحدات الرئيسية

| الوحدة | الوصف | الرابط |
|--------|--------|--------|
| 👥 العملاء | إدارة بيانات العملاء والأجهزة | `/customers` |
| 📋 طلبات الصيانة | تسجيل ومتابعة الطلبات | `/service-requests` |
| 📅 الجدولة | تنظيم المواعيد والمهندسين | `/scheduling` |
| 🔧 التنفيذ | تسجيل العمل المنجز | `/execution` |
| 📊 التقارير | الإحصائيات والتحليلات | `/reports` |

---

## 🎯 سيناريو الاستخدام الأساسي

### 1. إضافة عميل جديد
```
العملاء → إضافة عميل → ملء البيانات → حفظ
```

### 2. تسجيل طلب صيانة
```
طلبات الصيانة → طلب جديد → اختيار العميل → وصف العطل → حفظ
```

### 3. جدولة موعد
```
الجدولة → جدولة جديدة → اختيار الطلب والمهندس → تحديد الوقت → حفظ
```

### 4. تسجيل التنفيذ
```
التنفيذ → تقرير جديد → تسجيل العمل → إضافة قطع الغيار → حفظ
```

### 5. عرض التقارير
```
التقارير → اختيار نوع التقرير → عرض النتائج → تصدير
```

---

## 🔧 إعدادات أولية مهمة

### إضافة مهندسين:
```sql
INSERT INTO engineers (full_name, phone, specialization, is_active) VALUES
('أحمد محمد', '+966501234567', 'أجهزة التكييف', true),
('سارة أحمد', '+966507654321', 'الأجهزة المنزلية', true);
```

### إضافة قطع غيار:
```sql
INSERT INTO parts (part_name, part_number, unit_price, stock_quantity) VALUES
('فلتر هواء', 'FILTER-001', 25.00, 50),
('غاز تبريد', 'GAS-R410A', 150.00, 20);
```

---

## 🚨 حل المشاكل السريع

| المشكلة | الحل |
|---------|------|
| خطأ اتصال قاعدة البيانات | تحقق من `.env.local` |
| الصفحة لا تظهر | تأكد من تشغيل `npm run dev` |
| خطأ في التبعيات | شغل `npm install` |
| مشاكل في التصميم | امسح cache المتصفح |

---

## 📞 الدعم السريع

1. **تحقق من الأخطاء:** افتح Developer Tools (F12)
2. **راجع الملفات:** تأكد من وجود جميع الملفات
3. **أعد التشغيل:** أوقف الخادم وأعد تشغيله
4. **راجع الوثائق:** `SETUP.md` للتفاصيل الكاملة

---

## ✅ قائمة التحقق

- [ ] Node.js 18+ مثبت
- [ ] حساب Supabase منشأ
- [ ] ملف `.env.local` معدّل
- [ ] قاعدة البيانات منشأة
- [ ] التبعيات مثبتة (`npm install`)
- [ ] النظام يعمل (`npm run dev`)
- [ ] الصفحة الرئيسية تظهر
- [ ] يمكن إضافة عميل جديد

---

**🎉 مبروك! النظام جاهز للاستخدام**
