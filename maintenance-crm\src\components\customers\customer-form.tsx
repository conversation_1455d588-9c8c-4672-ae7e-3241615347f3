'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { CustomerFormData } from '@/types'
import { Save, X } from 'lucide-react'

const customerSchema = z.object({
  full_name: z.string().min(2, 'الاسم الكامل مطلوب (حد أدنى حرفين)'),
  primary_phone: z.string().min(10, 'رقم الهاتف الأساسي مطلوب').regex(/^[+]?[0-9\s-()]+$/, 'رقم هاتف غير صحيح'),
  secondary_phone: z.string().optional().refine((val) => !val || /^[+]?[0-9\s-()]+$/.test(val), 'رقم هاتف غير صحيح'),
  email: z.string().email('بريد إلكتروني غير صحيح').optional().or(z.literal('')),
  detailed_address: z.string().min(10, 'العنوان التفصيلي مطلوب (حد أدنى 10 أحرف)'),
  landmark: z.string().optional(),
  contact_person: z.string().optional(),
  special_notes: z.string().optional(),
})

interface CustomerFormProps {
  initialData?: Partial<CustomerFormData>
  onSubmit: (data: CustomerFormData) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}

export function CustomerForm({ initialData, onSubmit, onCancel, isLoading }: CustomerFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm<CustomerFormData>({
    resolver: zodResolver(customerSchema),
    defaultValues: initialData || {
      full_name: '',
      primary_phone: '',
      secondary_phone: '',
      email: '',
      detailed_address: '',
      landmark: '',
      contact_person: '',
      special_notes: '',
    }
  })

  const handleFormSubmit = async (data: CustomerFormData) => {
    setIsSubmitting(true)
    try {
      await onSubmit(data)
      if (!initialData) {
        reset() // Reset form only for new customers
      }
    } catch (error) {
      console.error('Error submitting customer form:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {initialData ? 'تعديل بيانات العميل' : 'إضافة عميل جديد'}
        </CardTitle>
        <CardDescription>
          {initialData 
            ? 'قم بتعديل بيانات العميل أدناه' 
            : 'أدخل بيانات العميل الجديد أدناه'
          }
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">المعلومات الأساسية</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="full_name">الاسم الكامل *</Label>
                <Input
                  id="full_name"
                  {...register('full_name')}
                  placeholder="أدخل الاسم الكامل"
                  className={errors.full_name ? 'border-red-500' : ''}
                />
                {errors.full_name && (
                  <p className="text-sm text-red-500">{errors.full_name.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="contact_person">جهة الاتصال (للمؤسسات)</Label>
                <Input
                  id="contact_person"
                  {...register('contact_person')}
                  placeholder="اسم الشخص المسؤول"
                />
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">معلومات الاتصال</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="primary_phone">رقم الهاتف الأساسي *</Label>
                <Input
                  id="primary_phone"
                  {...register('primary_phone')}
                  placeholder="+966 5X XXX XXXX"
                  className={errors.primary_phone ? 'border-red-500' : ''}
                />
                {errors.primary_phone && (
                  <p className="text-sm text-red-500">{errors.primary_phone.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="secondary_phone">رقم الهاتف الاحتياطي</Label>
                <Input
                  id="secondary_phone"
                  {...register('secondary_phone')}
                  placeholder="+966 5X XXX XXXX"
                  className={errors.secondary_phone ? 'border-red-500' : ''}
                />
                {errors.secondary_phone && (
                  <p className="text-sm text-red-500">{errors.secondary_phone.message}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">البريد الإلكتروني</Label>
              <Input
                id="email"
                type="email"
                {...register('email')}
                placeholder="<EMAIL>"
                className={errors.email ? 'border-red-500' : ''}
              />
              {errors.email && (
                <p className="text-sm text-red-500">{errors.email.message}</p>
              )}
            </div>
          </div>

          {/* Address Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">معلومات العنوان</h3>
            
            <div className="space-y-2">
              <Label htmlFor="detailed_address">العنوان التفصيلي *</Label>
              <Textarea
                id="detailed_address"
                {...register('detailed_address')}
                placeholder="أدخل العنوان التفصيلي مع اسم الحي والشارع"
                className={errors.detailed_address ? 'border-red-500' : ''}
                rows={3}
              />
              {errors.detailed_address && (
                <p className="text-sm text-red-500">{errors.detailed_address.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="landmark">معلم بارز</Label>
              <Input
                id="landmark"
                {...register('landmark')}
                placeholder="أقرب معلم بارز للعنوان"
              />
            </div>
          </div>

          {/* Additional Notes */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">ملاحظات إضافية</h3>
            
            <div className="space-y-2">
              <Label htmlFor="special_notes">ملاحظات خاصة</Label>
              <Textarea
                id="special_notes"
                {...register('special_notes')}
                placeholder="أي ملاحظات خاصة (أفضل وقت للاتصال، حساسية معينة، إلخ)"
                rows={3}
              />
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex gap-4 pt-6">
            <Button
              type="submit"
              disabled={isSubmitting || isLoading}
              className="flex-1"
            >
              <Save className="h-4 w-4 ml-2" />
              {isSubmitting ? 'جاري الحفظ...' : 'حفظ البيانات'}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting || isLoading}
            >
              <X className="h-4 w-4 ml-2" />
              إلغاء
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
