'use client'

import { <PERSON>er, <PERSON>ce, ServiceRequest } from '@/types'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Phone, 
  Mail, 
  MapPin, 
  Edit, 
  Plus,
  User,
  Building,
  Calendar,
  Wrench,
  Monitor,
  Clock
} from 'lucide-react'
import { formatDate, formatDateTime } from '@/lib/utils'

interface CustomerDetailsProps {
  customer: Customer
  devices?: Device[]
  recentRequests?: ServiceRequest[]
  onEdit: () => void
  onAddDevice: () => void
  onAddServiceRequest: () => void
  isLoading?: boolean
}

export function CustomerDetails({ 
  customer, 
  devices = [], 
  recentRequests = [],
  onEdit, 
  onAddDevice, 
  onAddServiceRequest,
  isLoading 
}: CustomerDetailsProps) {
  const isCompany = (customerName: string) => {
    const companyKeywords = ['شركة', 'مؤسسة', 'مجموعة', 'شراكة', 'تجارة', 'خدمات']
    return companyKeywords.some(keyword => customerName.includes(keyword))
  }

  const getStatusColor = (status: string) => {
    const statusColors: Record<string, string> = {
      'قيد المراجعة': 'bg-yellow-100 text-yellow-800',
      'تم تسجيله': 'bg-blue-100 text-blue-800',
      'في انتظار فني': 'bg-orange-100 text-orange-800',
      'تم تحديد موعد': 'bg-purple-100 text-purple-800',
      'في الطريق للعميل': 'bg-indigo-100 text-indigo-800',
      'قيد التنفيذ': 'bg-green-100 text-green-800',
      'مكتمل': 'bg-green-100 text-green-800',
      'مغلق': 'bg-gray-100 text-gray-800',
    }
    return statusColors[status] || 'bg-gray-100 text-gray-800'
  }

  const getPriorityColor = (priority: string) => {
    const priorityColors: Record<string, string> = {
      'عاجل': 'bg-red-100 text-red-800',
      'متوسط': 'bg-yellow-100 text-yellow-800',
      'منخفض': 'bg-green-100 text-green-800',
    }
    return priorityColors[priority] || 'bg-gray-100 text-gray-800'
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card className="animate-pulse">
          <CardContent className="p-6">
            <div className="space-y-4">
              <div className="h-6 bg-gray-200 rounded w-1/3"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              <div className="h-4 bg-gray-200 rounded w-2/3"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Customer Information Card */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-3">
              {isCompany(customer.full_name) ? (
                <Building className="h-8 w-8 text-blue-600" />
              ) : (
                <User className="h-8 w-8 text-green-600" />
              )}
              <div>
                <CardTitle className="text-2xl">{customer.full_name}</CardTitle>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant={isCompany(customer.full_name) ? "default" : "secondary"}>
                    {isCompany(customer.full_name) ? 'مؤسسة' : 'فرد'}
                  </Badge>
                  {customer.contact_person && (
                    <span className="text-sm text-gray-600">
                      جهة الاتصال: {customer.contact_person}
                    </span>
                  )}
                </div>
              </div>
            </div>
            <Button onClick={onEdit} variant="outline">
              <Edit className="h-4 w-4 ml-2" />
              تعديل البيانات
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Contact Information */}
          <div>
            <h3 className="text-lg font-semibold mb-3">معلومات الاتصال</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-3">
                <Phone className="h-5 w-5 text-gray-400" />
                <div>
                  <p className="font-medium">{customer.primary_phone}</p>
                  <p className="text-sm text-gray-500">الهاتف الأساسي</p>
                </div>
              </div>
              {customer.secondary_phone && (
                <div className="flex items-center gap-3">
                  <Phone className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="font-medium">{customer.secondary_phone}</p>
                    <p className="text-sm text-gray-500">الهاتف الاحتياطي</p>
                  </div>
                </div>
              )}
              {customer.email && (
                <div className="flex items-center gap-3 md:col-span-2">
                  <Mail className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="font-medium">{customer.email}</p>
                    <p className="text-sm text-gray-500">البريد الإلكتروني</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Address Information */}
          <div>
            <h3 className="text-lg font-semibold mb-3">معلومات العنوان</h3>
            <div className="flex items-start gap-3">
              <MapPin className="h-5 w-5 text-gray-400 mt-1" />
              <div className="flex-1">
                <p className="font-medium">{customer.detailed_address}</p>
                {customer.landmark && (
                  <p className="text-sm text-gray-500 mt-1">
                    <span className="font-medium">معلم بارز:</span> {customer.landmark}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Special Notes */}
          {customer.special_notes && (
            <div>
              <h3 className="text-lg font-semibold mb-3">ملاحظات خاصة</h3>
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <p className="text-gray-700">{customer.special_notes}</p>
              </div>
            </div>
          )}

          {/* Metadata */}
          <div className="border-t pt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-500">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                <span>تاريخ الإنشاء: {formatDate(customer.created_at)}</span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                <span>آخر تحديث: {formatDate(customer.updated_at)}</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Devices Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Monitor className="h-5 w-5" />
                الأجهزة ({devices.length})
              </CardTitle>
              <CardDescription>
                قائمة الأجهزة المسجلة لهذا العميل
              </CardDescription>
            </div>
            <Button onClick={onAddDevice} size="sm">
              <Plus className="h-4 w-4 ml-2" />
              إضافة جهاز
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {devices.length === 0 ? (
            <div className="text-center py-8">
              <Monitor className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 mb-4">لا توجد أجهزة مسجلة لهذا العميل</p>
              <Button onClick={onAddDevice} variant="outline">
                <Plus className="h-4 w-4 ml-2" />
                إضافة جهاز جديد
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {devices.map((device) => (
                <div key={device.id} className="border rounded-lg p-4">
                  <div className="flex items-start justify-between mb-2">
                    <h4 className="font-semibold">{device.device_name}</h4>
                    <Badge variant="outline">{device.model}</Badge>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">
                    الرقم التسلسلي: {device.serial_number}
                  </p>
                  {device.warranty_end_date && (
                    <p className="text-sm text-gray-500">
                      انتهاء الضمان: {formatDate(device.warranty_end_date)}
                    </p>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recent Service Requests */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Wrench className="h-5 w-5" />
                طلبات الصيانة الأخيرة ({recentRequests.length})
              </CardTitle>
              <CardDescription>
                آخر طلبات الصيانة لهذا العميل
              </CardDescription>
            </div>
            <Button onClick={onAddServiceRequest} size="sm">
              <Plus className="h-4 w-4 ml-2" />
              طلب صيانة جديد
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {recentRequests.length === 0 ? (
            <div className="text-center py-8">
              <Wrench className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 mb-4">لا توجد طلبات صيانة لهذا العميل</p>
              <Button onClick={onAddServiceRequest} variant="outline">
                <Plus className="h-4 w-4 ml-2" />
                إنشاء طلب صيانة جديد
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {recentRequests.slice(0, 5).map((request) => (
                <div key={request.id} className="border rounded-lg p-4">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <p className="font-medium line-clamp-2">{request.initial_description}</p>
                      <p className="text-sm text-gray-500 mt-1">
                        {formatDateTime(request.request_date)}
                      </p>
                    </div>
                    <div className="flex gap-2 ml-4">
                      <Badge className={getPriorityColor(request.priority)}>
                        {request.priority}
                      </Badge>
                      <Badge className={getStatusColor(request.status)}>
                        {request.status}
                      </Badge>
                    </div>
                  </div>
                </div>
              ))}
              {recentRequests.length > 5 && (
                <div className="text-center pt-4">
                  <Button variant="outline" size="sm">
                    عرض جميع الطلبات ({recentRequests.length})
                  </Button>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
