'use client'

import { useState } from 'react'
import { ServiceRequest, Priority, RequestStatus } from '@/types'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  Search, 
  Filter,
  Eye, 
  Edit, 
  Plus,
  ClipboardList,
  Calendar,
  User,
  Monitor,
  Clock
} from 'lucide-react'
import { formatDateTime, formatDate } from '@/lib/utils'

interface ServiceRequestListProps {
  serviceRequests: ServiceRequest[]
  onEdit: (request: ServiceRequest) => void
  onView: (request: ServiceRequest) => void
  onAdd: () => void
  isLoading?: boolean
}

interface Filters {
  search: string
  status: RequestStatus | 'all'
  priority: Priority | 'all'
}

export function ServiceRequestList({ 
  serviceRequests, 
  onEdit, 
  onView, 
  onAdd, 
  isLoading 
}: ServiceRequestListProps) {
  const [filters, setFilters] = useState<Filters>({
    search: '',
    status: 'all',
    priority: 'all'
  })
  const [showFilters, setShowFilters] = useState(false)

  // Filter service requests based on current filters
  const filteredRequests = serviceRequests.filter(request => {
    const matchesSearch = !filters.search.trim() || 
      request.initial_description.toLowerCase().includes(filters.search.toLowerCase()) ||
      request.customer?.full_name.toLowerCase().includes(filters.search.toLowerCase()) ||
      request.device?.device_name.toLowerCase().includes(filters.search.toLowerCase()) ||
      request.received_by.toLowerCase().includes(filters.search.toLowerCase())

    const matchesStatus = filters.status === 'all' || request.status === filters.status
    const matchesPriority = filters.priority === 'all' || request.priority === filters.priority

    return matchesSearch && matchesStatus && matchesPriority
  })

  const getStatusColor = (status: RequestStatus) => {
    const statusColors: Record<RequestStatus, string> = {
      'قيد المراجعة': 'bg-yellow-100 text-yellow-800 border-yellow-200',
      'تم تسجيله': 'bg-blue-100 text-blue-800 border-blue-200',
      'في انتظار فني': 'bg-orange-100 text-orange-800 border-orange-200',
      'تم تحديد موعد': 'bg-purple-100 text-purple-800 border-purple-200',
      'في الطريق للعميل': 'bg-indigo-100 text-indigo-800 border-indigo-200',
      'قيد التنفيذ': 'bg-green-100 text-green-800 border-green-200',
      'مكتمل': 'bg-green-100 text-green-800 border-green-200',
      'مغلق': 'bg-gray-100 text-gray-800 border-gray-200',
    }
    return statusColors[status] || 'bg-gray-100 text-gray-800 border-gray-200'
  }

  const getPriorityColor = (priority: Priority) => {
    const priorityColors: Record<Priority, string> = {
      'عاجل': 'bg-red-100 text-red-800 border-red-200',
      'متوسط': 'bg-yellow-100 text-yellow-800 border-yellow-200',
      'منخفض': 'bg-green-100 text-green-800 border-green-200',
    }
    return priorityColors[priority] || 'bg-gray-100 text-gray-800 border-gray-200'
  }

  const resetFilters = () => {
    setFilters({
      search: '',
      status: 'all',
      priority: 'all'
    })
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="space-y-3">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header with Search and Filters */}
      <div className="flex flex-col gap-4">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex-1 max-w-md">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="البحث في طلبات الصيانة..."
                value={filters.search}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                className="pr-10"
              />
            </div>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2"
            >
              <Filter className="h-4 w-4" />
              المرشحات
            </Button>
            <Button onClick={onAdd} className="whitespace-nowrap">
              <Plus className="h-4 w-4 ml-2" />
              طلب صيانة جديد
            </Button>
          </div>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <Card>
            <CardContent className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">الحالة</label>
                  <Select
                    value={filters.status}
                    onValueChange={(value) => setFilters(prev => ({ 
                      ...prev, 
                      status: value as RequestStatus | 'all' 
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">جميع الحالات</SelectItem>
                      <SelectItem value="قيد المراجعة">قيد المراجعة</SelectItem>
                      <SelectItem value="تم تسجيله">تم تسجيله</SelectItem>
                      <SelectItem value="في انتظار فني">في انتظار فني</SelectItem>
                      <SelectItem value="تم تحديد موعد">تم تحديد موعد</SelectItem>
                      <SelectItem value="في الطريق للعميل">في الطريق للعميل</SelectItem>
                      <SelectItem value="قيد التنفيذ">قيد التنفيذ</SelectItem>
                      <SelectItem value="مكتمل">مكتمل</SelectItem>
                      <SelectItem value="مغلق">مغلق</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">الأولوية</label>
                  <Select
                    value={filters.priority}
                    onValueChange={(value) => setFilters(prev => ({ 
                      ...prev, 
                      priority: value as Priority | 'all' 
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">جميع الأولويات</SelectItem>
                      <SelectItem value="عاجل">عاجل</SelectItem>
                      <SelectItem value="متوسط">متوسط</SelectItem>
                      <SelectItem value="منخفض">منخفض</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-end">
                  <Button variant="outline" onClick={resetFilters} className="w-full">
                    إعادة تعيين المرشحات
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Results Summary */}
      <div className="text-sm text-gray-600">
        {filters.search || filters.status !== 'all' || filters.priority !== 'all' ? (
          <span>
            تم العثور على {filteredRequests.length} طلب من أصل {serviceRequests.length}
          </span>
        ) : (
          <span>إجمالي طلبات الصيانة: {serviceRequests.length}</span>
        )}
      </div>

      {/* Service Request Cards */}
      <div className="space-y-4">
        {filteredRequests.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <ClipboardList className="h-12 w-12 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {filters.search || filters.status !== 'all' || filters.priority !== 'all'
                ? 'لم يتم العثور على نتائج'
                : 'لا توجد طلبات صيانة'
              }
            </h3>
            <p className="text-gray-500 mb-4">
              {filters.search || filters.status !== 'all' || filters.priority !== 'all'
                ? 'جرب تغيير المرشحات أو كلمات البحث'
                : 'ابدأ بإنشاء طلب صيانة جديد'
              }
            </p>
            {(!filters.search && filters.status === 'all' && filters.priority === 'all') && (
              <Button onClick={onAdd}>
                <Plus className="h-4 w-4 ml-2" />
                طلب صيانة جديد
              </Button>
            )}
          </div>
        ) : (
          filteredRequests.map((request) => (
            <Card key={request.id} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <Badge className={getPriorityColor(request.priority)}>
                        {request.priority}
                      </Badge>
                      <Badge className={getStatusColor(request.status)}>
                        {request.status}
                      </Badge>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                      {request.initial_description}
                    </h3>
                  </div>
                  <div className="flex gap-2 ml-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onView(request)}
                    >
                      <Eye className="h-4 w-4 ml-1" />
                      عرض
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onEdit(request)}
                    >
                      <Edit className="h-4 w-4 ml-1" />
                      تعديل
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="font-medium">{request.customer?.full_name}</p>
                      <p className="text-xs">{request.customer?.primary_phone}</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Monitor className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="font-medium">{request.device?.device_name}</p>
                      <p className="text-xs">{request.device?.model}</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="font-medium">{formatDate(request.request_date)}</p>
                      <p className="text-xs">{formatDateTime(request.request_date)}</p>
                    </div>
                  </div>
                </div>

                <div className="mt-4 pt-4 border-t border-gray-100">
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>مستقبل البلاغ: {request.received_by}</span>
                    <span>المصدر: {request.request_source}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  )
}
