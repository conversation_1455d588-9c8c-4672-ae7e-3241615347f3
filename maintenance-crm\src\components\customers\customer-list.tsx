'use client'

import { useState } from 'react'
import { Customer } from '@/types'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Search, 
  Phone, 
  Mail, 
  MapPin, 
  Edit, 
  Eye, 
  Plus,
  User,
  Building
} from 'lucide-react'
import { formatDate } from '@/lib/utils'

interface CustomerListProps {
  customers: Customer[]
  onEdit: (customer: Customer) => void
  onView: (customer: Customer) => void
  onAdd: () => void
  isLoading?: boolean
}

export function CustomerList({ customers, onEdit, onView, onAdd, isLoading }: CustomerListProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [filteredCustomers, setFilteredCustomers] = useState(customers)

  // Filter customers based on search term
  const handleSearch = (term: string) => {
    setSearchTerm(term)
    if (!term.trim()) {
      setFilteredCustomers(customers)
      return
    }

    const filtered = customers.filter(customer =>
      customer.full_name.toLowerCase().includes(term.toLowerCase()) ||
      customer.primary_phone.includes(term) ||
      customer.email?.toLowerCase().includes(term.toLowerCase()) ||
      customer.detailed_address.toLowerCase().includes(term.toLowerCase())
    )
    setFilteredCustomers(filtered)
  }

  // Update filtered customers when customers prop changes
  useState(() => {
    if (!searchTerm.trim()) {
      setFilteredCustomers(customers)
    } else {
      handleSearch(searchTerm)
    }
  }, [customers])

  const isCompany = (customerName: string) => {
    const companyKeywords = ['شركة', 'مؤسسة', 'مجموعة', 'شراكة', 'تجارة', 'خدمات']
    return companyKeywords.some(keyword => customerName.includes(keyword))
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="space-y-3">
                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/3"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header with Search and Add Button */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex-1 max-w-md">
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="البحث عن عميل (الاسم، الهاتف، البريد الإلكتروني، العنوان)"
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="pr-10"
            />
          </div>
        </div>
        <Button onClick={onAdd} className="whitespace-nowrap">
          <Plus className="h-4 w-4 ml-2" />
          إضافة عميل جديد
        </Button>
      </div>

      {/* Results Summary */}
      <div className="text-sm text-gray-600">
        {searchTerm ? (
          <span>
            تم العثور على {filteredCustomers.length} عميل من أصل {customers.length}
          </span>
        ) : (
          <span>إجمالي العملاء: {customers.length}</span>
        )}
      </div>

      {/* Customer Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredCustomers.length === 0 ? (
          <div className="col-span-full text-center py-12">
            <div className="text-gray-400 mb-4">
              <User className="h-12 w-12 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchTerm ? 'لم يتم العثور على نتائج' : 'لا توجد عملاء'}
            </h3>
            <p className="text-gray-500 mb-4">
              {searchTerm 
                ? 'جرب تغيير كلمات البحث أو إزالة المرشحات'
                : 'ابدأ بإضافة عميل جديد للنظام'
              }
            </p>
            {!searchTerm && (
              <Button onClick={onAdd}>
                <Plus className="h-4 w-4 ml-2" />
                إضافة عميل جديد
              </Button>
            )}
          </div>
        ) : (
          filteredCustomers.map((customer) => (
            <Card key={customer.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2">
                    {isCompany(customer.full_name) ? (
                      <Building className="h-5 w-5 text-blue-600" />
                    ) : (
                      <User className="h-5 w-5 text-green-600" />
                    )}
                    <CardTitle className="text-lg leading-tight">
                      {customer.full_name}
                    </CardTitle>
                  </div>
                  <Badge variant={isCompany(customer.full_name) ? "default" : "secondary"}>
                    {isCompany(customer.full_name) ? 'مؤسسة' : 'فرد'}
                  </Badge>
                </div>
                {customer.contact_person && (
                  <p className="text-sm text-gray-600">
                    جهة الاتصال: {customer.contact_person}
                  </p>
                )}
              </CardHeader>
              <CardContent className="space-y-3">
                {/* Contact Information */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm">
                    <Phone className="h-4 w-4 text-gray-400" />
                    <span className="font-medium">{customer.primary_phone}</span>
                  </div>
                  {customer.secondary_phone && (
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Phone className="h-4 w-4 text-gray-400" />
                      <span>{customer.secondary_phone}</span>
                    </div>
                  )}
                  {customer.email && (
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Mail className="h-4 w-4 text-gray-400" />
                      <span className="truncate">{customer.email}</span>
                    </div>
                  )}
                </div>

                {/* Address */}
                <div className="flex items-start gap-2 text-sm text-gray-600">
                  <MapPin className="h-4 w-4 text-gray-400 mt-0.5 flex-shrink-0" />
                  <span className="line-clamp-2">{customer.detailed_address}</span>
                </div>

                {/* Landmark */}
                {customer.landmark && (
                  <div className="text-sm text-gray-500">
                    <span className="font-medium">معلم بارز:</span> {customer.landmark}
                  </div>
                )}

                {/* Special Notes */}
                {customer.special_notes && (
                  <div className="text-sm text-gray-500 bg-yellow-50 p-2 rounded">
                    <span className="font-medium">ملاحظة:</span> {customer.special_notes}
                  </div>
                )}

                {/* Creation Date */}
                <div className="text-xs text-gray-400 pt-2 border-t">
                  تم الإنشاء: {formatDate(customer.created_at)}
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2 pt-3">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onView(customer)}
                    className="flex-1"
                  >
                    <Eye className="h-4 w-4 ml-1" />
                    عرض
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onEdit(customer)}
                    className="flex-1"
                  >
                    <Edit className="h-4 w-4 ml-1" />
                    تعديل
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  )
}
