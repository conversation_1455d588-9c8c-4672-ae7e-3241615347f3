'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { DeviceFormData } from '@/types'
import { Save, X, Monitor } from 'lucide-react'

const deviceSchema = z.object({
  device_name: z.string().min(2, 'اسم الجهاز مطلوب (حد أدنى حرفين)'),
  model: z.string().min(2, 'موديل الجهاز مطلوب'),
  serial_number: z.string().min(3, 'الرقم التسلسلي مطلوب'),
  purchase_date: z.string().optional(),
  warranty_end_date: z.string().optional(),
  original_invoice_number: z.string().optional(),
})

interface DeviceFormProps {
  initialData?: Partial<DeviceFormData>
  onSubmit: (data: DeviceFormData) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}

export function DeviceForm({ initialData, onSubmit, onCancel, isLoading }: DeviceFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch
  } = useForm<DeviceFormData>({
    resolver: zodResolver(deviceSchema),
    defaultValues: initialData || {
      device_name: '',
      model: '',
      serial_number: '',
      purchase_date: '',
      warranty_end_date: '',
      original_invoice_number: '',
    }
  })

  const purchaseDate = watch('purchase_date')

  const handleFormSubmit = async (data: DeviceFormData) => {
    setIsSubmitting(true)
    try {
      await onSubmit(data)
      if (!initialData) {
        reset() // Reset form only for new devices
      }
    } catch (error) {
      console.error('Error submitting device form:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Monitor className="h-6 w-6 text-blue-600" />
          {initialData ? 'تعديل بيانات الجهاز' : 'إضافة جهاز جديد'}
        </CardTitle>
        <CardDescription>
          {initialData 
            ? 'قم بتعديل بيانات الجهاز أدناه' 
            : 'أدخل بيانات الجهاز الجديد أدناه'
          }
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {/* Device Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">معلومات الجهاز</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="device_name">اسم الجهاز *</Label>
                <Input
                  id="device_name"
                  {...register('device_name')}
                  placeholder="مثال: مكيف سبليت، ثلاجة، غسالة"
                  className={errors.device_name ? 'border-red-500' : ''}
                />
                {errors.device_name && (
                  <p className="text-sm text-red-500">{errors.device_name.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="model">الموديل *</Label>
                <Input
                  id="model"
                  {...register('model')}
                  placeholder="مثال: Samsung AR24, LG GR-X247"
                  className={errors.model ? 'border-red-500' : ''}
                />
                {errors.model && (
                  <p className="text-sm text-red-500">{errors.model.message}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="serial_number">الرقم التسلسلي *</Label>
              <Input
                id="serial_number"
                {...register('serial_number')}
                placeholder="أدخل الرقم التسلسلي للجهاز"
                className={errors.serial_number ? 'border-red-500' : ''}
              />
              {errors.serial_number && (
                <p className="text-sm text-red-500">{errors.serial_number.message}</p>
              )}
              <p className="text-sm text-gray-500">
                الرقم التسلسلي يجب أن يكون فريداً لكل جهاز
              </p>
            </div>
          </div>

          {/* Purchase and Warranty Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">معلومات الشراء والضمان</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="purchase_date">تاريخ الشراء</Label>
                <Input
                  id="purchase_date"
                  type="date"
                  {...register('purchase_date')}
                  className={errors.purchase_date ? 'border-red-500' : ''}
                />
                {errors.purchase_date && (
                  <p className="text-sm text-red-500">{errors.purchase_date.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="warranty_end_date">تاريخ انتهاء الضمان</Label>
                <Input
                  id="warranty_end_date"
                  type="date"
                  {...register('warranty_end_date')}
                  min={purchaseDate || undefined}
                  className={errors.warranty_end_date ? 'border-red-500' : ''}
                />
                {errors.warranty_end_date && (
                  <p className="text-sm text-red-500">{errors.warranty_end_date.message}</p>
                )}
                {purchaseDate && (
                  <p className="text-sm text-gray-500">
                    يجب أن يكون تاريخ انتهاء الضمان بعد تاريخ الشراء
                  </p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="original_invoice_number">رقم الفاتورة الأصلية</Label>
              <Input
                id="original_invoice_number"
                {...register('original_invoice_number')}
                placeholder="رقم فاتورة الشراء الأصلية"
              />
              <p className="text-sm text-gray-500">
                رقم الفاتورة الأصلية للجهاز (اختياري)
              </p>
            </div>
          </div>

          {/* Warranty Status Indicator */}
          {purchaseDate && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">معلومات الضمان</h4>
              <div className="text-sm text-blue-700">
                {(() => {
                  const purchase = new Date(purchaseDate)
                  const today = new Date()
                  const daysSincePurchase = Math.floor((today.getTime() - purchase.getTime()) / (1000 * 60 * 60 * 24))
                  
                  if (daysSincePurchase < 0) {
                    return 'تاريخ الشراء في المستقبل - يرجى التحقق من التاريخ'
                  } else if (daysSincePurchase === 0) {
                    return 'تم شراء الجهاز اليوم'
                  } else if (daysSincePurchase < 30) {
                    return `تم شراء الجهاز منذ ${daysSincePurchase} يوم`
                  } else if (daysSincePurchase < 365) {
                    const months = Math.floor(daysSincePurchase / 30)
                    return `تم شراء الجهاز منذ ${months} شهر`
                  } else {
                    const years = Math.floor(daysSincePurchase / 365)
                    const remainingMonths = Math.floor((daysSincePurchase % 365) / 30)
                    return `تم شراء الجهاز منذ ${years} سنة${remainingMonths > 0 ? ` و ${remainingMonths} شهر` : ''}`
                  }
                })()}
              </div>
            </div>
          )}

          {/* Form Actions */}
          <div className="flex gap-4 pt-6">
            <Button
              type="submit"
              disabled={isSubmitting || isLoading}
              className="flex-1"
            >
              <Save className="h-4 w-4 ml-2" />
              {isSubmitting ? 'جاري الحفظ...' : 'حفظ البيانات'}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting || isLoading}
            >
              <X className="h-4 w-4 ml-2" />
              إلغاء
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
