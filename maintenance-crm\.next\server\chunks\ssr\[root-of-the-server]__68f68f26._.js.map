{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mY/AI/7-2025/CRM%20AGMENT/maintenance-crm/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: string | Date): string {\n  const d = new Date(date)\n  return d.toLocaleDateString('ar-SA', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  })\n}\n\nexport function formatDateTime(date: string | Date): string {\n  const d = new Date(date)\n  return d.toLocaleString('ar-SA', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit'\n  })\n}\n\nexport function formatTime(date: string | Date): string {\n  const d = new Date(date)\n  return d.toLocaleTimeString('ar-SA', {\n    hour: '2-digit',\n    minute: '2-digit'\n  })\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,eAAe,IAAmB;IAChD,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,cAAc,CAAC,SAAS;QAC/B,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;AACF;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,QAAQ;IACV;AACF;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mY/AI/7-2025/CRM%20AGMENT/maintenance-crm/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 147, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mY/AI/7-2025/CRM%20AGMENT/maintenance-crm/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mY/AI/7-2025/CRM%20AGMENT/maintenance-crm/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,iKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 207, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mY/AI/7-2025/CRM%20AGMENT/maintenance-crm/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mY/AI/7-2025/CRM%20AGMENT/maintenance-crm/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 316, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mY/AI/7-2025/CRM%20AGMENT/maintenance-crm/src/components/customers/customer-form.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useForm } from 'react-hook-form'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport * as z from 'zod'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { CustomerFormData } from '@/types'\nimport { Save, X } from 'lucide-react'\n\nconst customerSchema = z.object({\n  full_name: z.string().min(2, 'الاسم الكامل مطلوب (حد أدنى حرفين)'),\n  primary_phone: z.string().min(10, 'رقم الهاتف الأساسي مطلوب').regex(/^[+]?[0-9\\s-()]+$/, 'رقم هاتف غير صحيح'),\n  secondary_phone: z.string().optional().refine((val) => !val || /^[+]?[0-9\\s-()]+$/.test(val), 'رقم هاتف غير صحيح'),\n  email: z.string().email('بريد إلكتروني غير صحيح').optional().or(z.literal('')),\n  detailed_address: z.string().min(10, 'العنوان التفصيلي مطلوب (حد أدنى 10 أحرف)'),\n  landmark: z.string().optional(),\n  contact_person: z.string().optional(),\n  special_notes: z.string().optional(),\n})\n\ninterface CustomerFormProps {\n  initialData?: Partial<CustomerFormData>\n  onSubmit: (data: CustomerFormData) => Promise<void>\n  onCancel: () => void\n  isLoading?: boolean\n}\n\nexport function CustomerForm({ initialData, onSubmit, onCancel, isLoading }: CustomerFormProps) {\n  const [isSubmitting, setIsSubmitting] = useState(false)\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n    reset\n  } = useForm<CustomerFormData>({\n    resolver: zodResolver(customerSchema),\n    defaultValues: initialData || {\n      full_name: '',\n      primary_phone: '',\n      secondary_phone: '',\n      email: '',\n      detailed_address: '',\n      landmark: '',\n      contact_person: '',\n      special_notes: '',\n    }\n  })\n\n  const handleFormSubmit = async (data: CustomerFormData) => {\n    setIsSubmitting(true)\n    try {\n      await onSubmit(data)\n      if (!initialData) {\n        reset() // Reset form only for new customers\n      }\n    } catch (error) {\n      console.error('Error submitting customer form:', error)\n    } finally {\n      setIsSubmitting(false)\n    }\n  }\n\n  return (\n    <Card className=\"w-full max-w-2xl mx-auto\">\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          {initialData ? 'تعديل بيانات العميل' : 'إضافة عميل جديد'}\n        </CardTitle>\n        <CardDescription>\n          {initialData \n            ? 'قم بتعديل بيانات العميل أدناه' \n            : 'أدخل بيانات العميل الجديد أدناه'\n          }\n        </CardDescription>\n      </CardHeader>\n      <CardContent>\n        <form onSubmit={handleSubmit(handleFormSubmit)} className=\"space-y-6\">\n          {/* Basic Information */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">المعلومات الأساسية</h3>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"full_name\">الاسم الكامل *</Label>\n                <Input\n                  id=\"full_name\"\n                  {...register('full_name')}\n                  placeholder=\"أدخل الاسم الكامل\"\n                  className={errors.full_name ? 'border-red-500' : ''}\n                />\n                {errors.full_name && (\n                  <p className=\"text-sm text-red-500\">{errors.full_name.message}</p>\n                )}\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"contact_person\">جهة الاتصال (للمؤسسات)</Label>\n                <Input\n                  id=\"contact_person\"\n                  {...register('contact_person')}\n                  placeholder=\"اسم الشخص المسؤول\"\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Contact Information */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">معلومات الاتصال</h3>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"primary_phone\">رقم الهاتف الأساسي *</Label>\n                <Input\n                  id=\"primary_phone\"\n                  {...register('primary_phone')}\n                  placeholder=\"+966 5X XXX XXXX\"\n                  className={errors.primary_phone ? 'border-red-500' : ''}\n                />\n                {errors.primary_phone && (\n                  <p className=\"text-sm text-red-500\">{errors.primary_phone.message}</p>\n                )}\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"secondary_phone\">رقم الهاتف الاحتياطي</Label>\n                <Input\n                  id=\"secondary_phone\"\n                  {...register('secondary_phone')}\n                  placeholder=\"+966 5X XXX XXXX\"\n                  className={errors.secondary_phone ? 'border-red-500' : ''}\n                />\n                {errors.secondary_phone && (\n                  <p className=\"text-sm text-red-500\">{errors.secondary_phone.message}</p>\n                )}\n              </div>\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"email\">البريد الإلكتروني</Label>\n              <Input\n                id=\"email\"\n                type=\"email\"\n                {...register('email')}\n                placeholder=\"<EMAIL>\"\n                className={errors.email ? 'border-red-500' : ''}\n              />\n              {errors.email && (\n                <p className=\"text-sm text-red-500\">{errors.email.message}</p>\n              )}\n            </div>\n          </div>\n\n          {/* Address Information */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">معلومات العنوان</h3>\n            \n            <div className=\"space-y-2\">\n              <Label htmlFor=\"detailed_address\">العنوان التفصيلي *</Label>\n              <Textarea\n                id=\"detailed_address\"\n                {...register('detailed_address')}\n                placeholder=\"أدخل العنوان التفصيلي مع اسم الحي والشارع\"\n                className={errors.detailed_address ? 'border-red-500' : ''}\n                rows={3}\n              />\n              {errors.detailed_address && (\n                <p className=\"text-sm text-red-500\">{errors.detailed_address.message}</p>\n              )}\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"landmark\">معلم بارز</Label>\n              <Input\n                id=\"landmark\"\n                {...register('landmark')}\n                placeholder=\"أقرب معلم بارز للعنوان\"\n              />\n            </div>\n          </div>\n\n          {/* Additional Notes */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">ملاحظات إضافية</h3>\n            \n            <div className=\"space-y-2\">\n              <Label htmlFor=\"special_notes\">ملاحظات خاصة</Label>\n              <Textarea\n                id=\"special_notes\"\n                {...register('special_notes')}\n                placeholder=\"أي ملاحظات خاصة (أفضل وقت للاتصال، حساسية معينة، إلخ)\"\n                rows={3}\n              />\n            </div>\n          </div>\n\n          {/* Form Actions */}\n          <div className=\"flex gap-4 pt-6\">\n            <Button\n              type=\"submit\"\n              disabled={isSubmitting || isLoading}\n              className=\"flex-1\"\n            >\n              <Save className=\"h-4 w-4 ml-2\" />\n              {isSubmitting ? 'جاري الحفظ...' : 'حفظ البيانات'}\n            </Button>\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={onCancel}\n              disabled={isSubmitting || isLoading}\n            >\n              <X className=\"h-4 w-4 ml-2\" />\n              إلغاء\n            </Button>\n          </div>\n        </form>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAZA;;;;;;;;;;;;AAcA,MAAM,iBAAiB,CAAA,GAAA,+IAAA,CAAA,SAAQ,AAAD,EAAE;IAC9B,WAAW,CAAA,GAAA,+IAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;IAC7B,eAAe,CAAA,GAAA,+IAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,IAAI,4BAA4B,KAAK,CAAC,qBAAqB;IACzF,iBAAiB,CAAA,GAAA,+IAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ,GAAG,MAAM,CAAC,CAAC,MAAQ,CAAC,OAAO,oBAAoB,IAAI,CAAC,MAAM;IAC9F,OAAO,CAAA,GAAA,+IAAA,CAAA,SAAQ,AAAD,IAAI,KAAK,CAAC,0BAA0B,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,+IAAA,CAAA,UAAS,AAAD,EAAE;IAC1E,kBAAkB,CAAA,GAAA,+IAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,IAAI;IACrC,UAAU,CAAA,GAAA,+IAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;IAC7B,gBAAgB,CAAA,GAAA,+IAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;IACnC,eAAe,CAAA,GAAA,+IAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;AACpC;AASO,SAAS,aAAa,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAqB;IAC5F,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACrB,KAAK,EACN,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAoB;QAC5B,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe,eAAe;YAC5B,WAAW;YACX,eAAe;YACf,iBAAiB;YACjB,OAAO;YACP,kBAAkB;YAClB,UAAU;YACV,gBAAgB;YAChB,eAAe;QACjB;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,gBAAgB;QAChB,IAAI;YACF,MAAM,SAAS;YACf,IAAI,CAAC,aAAa;gBAChB,QAAQ,oCAAoC;;YAC9C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;;kCACT,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;kCAClB,cAAc,wBAAwB;;;;;;kCAEzC,8OAAC,gIAAA,CAAA,kBAAe;kCACb,cACG,kCACA;;;;;;;;;;;;0BAIR,8OAAC,gIAAA,CAAA,cAAW;0BACV,cAAA,8OAAC;oBAAK,UAAU,aAAa;oBAAmB,WAAU;;sCAExD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAEpD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAY;;;;;;8DAC3B,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACF,GAAG,SAAS,YAAY;oDACzB,aAAY;oDACZ,WAAW,OAAO,SAAS,GAAG,mBAAmB;;;;;;gDAElD,OAAO,SAAS,kBACf,8OAAC;oDAAE,WAAU;8DAAwB,OAAO,SAAS,CAAC,OAAO;;;;;;;;;;;;sDAIjE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAiB;;;;;;8DAChC,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACF,GAAG,SAAS,iBAAiB;oDAC9B,aAAY;;;;;;;;;;;;;;;;;;;;;;;;sCAOpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAEpD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAgB;;;;;;8DAC/B,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACF,GAAG,SAAS,gBAAgB;oDAC7B,aAAY;oDACZ,WAAW,OAAO,aAAa,GAAG,mBAAmB;;;;;;gDAEtD,OAAO,aAAa,kBACnB,8OAAC;oDAAE,WAAU;8DAAwB,OAAO,aAAa,CAAC,OAAO;;;;;;;;;;;;sDAIrE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAkB;;;;;;8DACjC,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACF,GAAG,SAAS,kBAAkB;oDAC/B,aAAY;oDACZ,WAAW,OAAO,eAAe,GAAG,mBAAmB;;;;;;gDAExD,OAAO,eAAe,kBACrB,8OAAC;oDAAE,WAAU;8DAAwB,OAAO,eAAe,CAAC,OAAO;;;;;;;;;;;;;;;;;;8CAKzE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAQ;;;;;;sDACvB,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACJ,GAAG,SAAS,QAAQ;4CACrB,aAAY;4CACZ,WAAW,OAAO,KAAK,GAAG,mBAAmB;;;;;;wCAE9C,OAAO,KAAK,kBACX,8OAAC;4CAAE,WAAU;sDAAwB,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;;;;;;;sCAM/D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAEpD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAmB;;;;;;sDAClC,8OAAC,oIAAA,CAAA,WAAQ;4CACP,IAAG;4CACF,GAAG,SAAS,mBAAmB;4CAChC,aAAY;4CACZ,WAAW,OAAO,gBAAgB,GAAG,mBAAmB;4CACxD,MAAM;;;;;;wCAEP,OAAO,gBAAgB,kBACtB,8OAAC;4CAAE,WAAU;sDAAwB,OAAO,gBAAgB,CAAC,OAAO;;;;;;;;;;;;8CAIxE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAW;;;;;;sDAC1B,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACF,GAAG,SAAS,WAAW;4CACxB,aAAY;;;;;;;;;;;;;;;;;;sCAMlB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAEpD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAgB;;;;;;sDAC/B,8OAAC,oIAAA,CAAA,WAAQ;4CACP,IAAG;4CACF,GAAG,SAAS,gBAAgB;4CAC7B,aAAY;4CACZ,MAAM;;;;;;;;;;;;;;;;;;sCAMZ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,UAAU,gBAAgB;oCAC1B,WAAU;;sDAEV,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCACf,eAAe,kBAAkB;;;;;;;8CAEpC,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU,gBAAgB;;sDAE1B,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5C", "debugId": null}}, {"offset": {"line": 827, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mY/AI/7-2025/CRM%20AGMENT/maintenance-crm/src/app/customers/new/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { CustomerForm } from '@/components/customers/customer-form'\nimport { Button } from '@/components/ui/button'\nimport { ArrowRight, Users } from 'lucide-react'\nimport Link from 'next/link'\n\nexport default function NewCustomerPage() {\n  const router = useRouter()\n  const [isLoading, setIsLoading] = useState(false)\n\n  const handleSubmit = async (data: any) => {\n    setIsLoading(true)\n    try {\n      // في التطبيق الحقيقي سيتم إرسال البيانات إلى Supabase\n      console.log('Creating new customer:', data)\n      \n      // محاكاة API call\n      await new Promise(resolve => setTimeout(resolve, 1000))\n      \n      // إظهار رسالة نجاح\n      alert('تم إضافة العميل بنجاح!')\n      \n      // العودة لصفحة العملاء\n      router.push('/customers')\n    } catch (error) {\n      console.error('Error creating customer:', error)\n      alert('حدث خطأ أثناء إضافة العميل')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleCancel = () => {\n    router.push('/customers')\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <div className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between py-6\">\n            <div className=\"flex items-center gap-4\">\n              <Link href=\"/customers\">\n                <Button variant=\"ghost\" className=\"flex items-center gap-2\">\n                  <ArrowRight className=\"h-4 w-4\" />\n                  العودة للعملاء\n                </Button>\n              </Link>\n              <div className=\"flex items-center gap-3\">\n                <Users className=\"h-8 w-8 text-blue-600\" />\n                <div>\n                  <h1 className=\"text-2xl font-bold text-gray-900\">\n                    إضافة عميل جديد\n                  </h1>\n                  <p className=\"text-gray-600\">\n                    أدخل بيانات العميل الجديد في النموذج أدناه\n                  </p>\n                </div>\n              </div>\n            </div>\n            <div className=\"flex items-center gap-4\">\n              <Link href=\"/\">\n                <Button variant=\"outline\">\n                  العودة للرئيسية\n                </Button>\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <CustomerForm\n          onSubmit={handleSubmit}\n          onCancel={handleCancel}\n          isLoading={isLoading}\n        />\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AAPA;;;;;;;;AASe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,eAAe,OAAO;QAC1B,aAAa;QACb,IAAI;YACF,sDAAsD;YACtD,QAAQ,GAAG,CAAC,0BAA0B;YAEtC,kBAAkB;YAClB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,mBAAmB;YACnB,MAAM;YAEN,uBAAuB;YACvB,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe;QACnB,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,WAAU;;8DAChC,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;kDAItC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAmC;;;;;;kEAGjD,8OAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;;;;;;;;;;;;;;0CAMnC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUpC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,mJAAA,CAAA,eAAY;oBACX,UAAU;oBACV,UAAU;oBACV,WAAW;;;;;;;;;;;;;;;;;AAKrB", "debugId": null}}]}