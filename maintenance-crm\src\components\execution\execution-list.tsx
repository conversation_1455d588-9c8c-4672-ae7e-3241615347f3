'use client'

import { useState } from 'react'
import { Execution } from '@/types'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  Search, 
  Filter,
  Eye, 
  Edit, 
  Plus,
  Wrench,
  Clock,
  User,
  CheckCircle,
  AlertCircle,
  FileText
} from 'lucide-react'
import { formatDateTime, formatDate, formatTime } from '@/lib/utils'

interface ExecutionListProps {
  executions: Execution[]
  onEdit: (execution: Execution) => void
  onView: (execution: Execution) => void
  onAdd: () => void
  isLoading?: boolean
}

interface Filters {
  search: string
  engineer: string
  status: string
  dateRange: string
}

export function ExecutionList({ 
  executions, 
  onEdit, 
  onView, 
  onAdd, 
  isLoading 
}: ExecutionListProps) {
  const [filters, setFilters] = useState<Filters>({
    search: '',
    engineer: 'all',
    status: 'all',
    dateRange: 'all'
  })
  const [showFilters, setShowFilters] = useState(false)

  // Get unique engineers for filter
  const uniqueEngineers = Array.from(
    new Set(executions.map(e => e.engineer?.id).filter(Boolean))
  ).map(id => executions.find(e => e.engineer?.id === id)?.engineer).filter(Boolean)

  // Filter executions based on current filters
  const filteredExecutions = executions.filter(execution => {
    const matchesSearch = !filters.search.trim() || 
      execution.technical_description?.toLowerCase().includes(filters.search.toLowerCase()) ||
      execution.repair_actions?.toLowerCase().includes(filters.search.toLowerCase()) ||
      execution.service_request?.customer?.full_name?.toLowerCase().includes(filters.search.toLowerCase()) ||
      execution.engineer?.full_name?.toLowerCase().includes(filters.search.toLowerCase()) ||
      execution.received_by_customer?.toLowerCase().includes(filters.search.toLowerCase())

    const matchesEngineer = filters.engineer === 'all' || execution.engineer_id === filters.engineer
    
    const matchesStatus = filters.status === 'all' || 
      (filters.status === 'completed' && execution.end_time) ||
      (filters.status === 'in_progress' && execution.start_time && !execution.end_time) ||
      (filters.status === 'not_started' && !execution.start_time)

    let matchesDateRange = true
    if (filters.dateRange !== 'all' && execution.created_at) {
      const executionDate = new Date(execution.created_at)
      const now = new Date()
      
      switch (filters.dateRange) {
        case 'today':
          matchesDateRange = executionDate.toDateString() === now.toDateString()
          break
        case 'week':
          const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          matchesDateRange = executionDate >= weekAgo
          break
        case 'month':
          const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          matchesDateRange = executionDate >= monthAgo
          break
      }
    }

    return matchesSearch && matchesEngineer && matchesStatus && matchesDateRange
  })

  const getExecutionStatus = (execution: Execution) => {
    if (execution.end_time) {
      return { status: 'مكتمل', color: 'bg-green-100 text-green-800', icon: CheckCircle }
    } else if (execution.start_time) {
      return { status: 'قيد التنفيذ', color: 'bg-blue-100 text-blue-800', icon: Clock }
    } else {
      return { status: 'لم يبدأ', color: 'bg-gray-100 text-gray-800', icon: AlertCircle }
    }
  }

  const calculateWorkDuration = (execution: Execution) => {
    if (execution.start_time && execution.end_time) {
      const start = new Date(execution.start_time)
      const end = new Date(execution.end_time)
      const diffMs = end.getTime() - start.getTime()
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
      const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60))
      
      if (diffMs > 0) {
        return `${diffHours}:${diffMinutes.toString().padStart(2, '0')}`
      }
    }
    return null
  }

  const resetFilters = () => {
    setFilters({
      search: '',
      engineer: 'all',
      status: 'all',
      dateRange: 'all'
    })
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="space-y-3">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header with Search and Filters */}
      <div className="flex flex-col gap-4">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex-1 max-w-md">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="البحث في تقارير التنفيذ..."
                value={filters.search}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                className="pr-10"
              />
            </div>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2"
            >
              <Filter className="h-4 w-4" />
              المرشحات
            </Button>
            <Button onClick={onAdd} className="whitespace-nowrap">
              <Plus className="h-4 w-4 ml-2" />
              تقرير تنفيذ جديد
            </Button>
          </div>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <Card>
            <CardContent className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">المهندس</label>
                  <Select
                    value={filters.engineer}
                    onValueChange={(value) => setFilters(prev => ({ ...prev, engineer: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">جميع المهندسين</SelectItem>
                      {uniqueEngineers.map((engineer) => (
                        <SelectItem key={engineer!.id} value={engineer!.id}>
                          {engineer!.full_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">الحالة</label>
                  <Select
                    value={filters.status}
                    onValueChange={(value) => setFilters(prev => ({ ...prev, status: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">جميع الحالات</SelectItem>
                      <SelectItem value="completed">مكتمل</SelectItem>
                      <SelectItem value="in_progress">قيد التنفيذ</SelectItem>
                      <SelectItem value="not_started">لم يبدأ</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">الفترة الزمنية</label>
                  <Select
                    value={filters.dateRange}
                    onValueChange={(value) => setFilters(prev => ({ ...prev, dateRange: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">جميع الفترات</SelectItem>
                      <SelectItem value="today">اليوم</SelectItem>
                      <SelectItem value="week">آخر أسبوع</SelectItem>
                      <SelectItem value="month">آخر شهر</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-end">
                  <Button variant="outline" onClick={resetFilters} className="w-full">
                    إعادة تعيين
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Results Summary */}
      <div className="text-sm text-gray-600">
        {filters.search || filters.engineer !== 'all' || filters.status !== 'all' || filters.dateRange !== 'all' ? (
          <span>
            تم العثور على {filteredExecutions.length} تقرير من أصل {executions.length}
          </span>
        ) : (
          <span>إجمالي تقارير التنفيذ: {executions.length}</span>
        )}
      </div>

      {/* Execution Cards */}
      <div className="space-y-4">
        {filteredExecutions.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <Wrench className="h-12 w-12 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {filters.search || filters.engineer !== 'all' || filters.status !== 'all' || filters.dateRange !== 'all'
                ? 'لم يتم العثور على نتائج'
                : 'لا توجد تقارير تنفيذ'
              }
            </h3>
            <p className="text-gray-500 mb-4">
              {filters.search || filters.engineer !== 'all' || filters.status !== 'all' || filters.dateRange !== 'all'
                ? 'جرب تغيير المرشحات أو كلمات البحث'
                : 'ابدأ بإنشاء تقرير تنفيذ جديد'
              }
            </p>
            {(!filters.search && filters.engineer === 'all' && filters.status === 'all' && filters.dateRange === 'all') && (
              <Button onClick={onAdd}>
                <Plus className="h-4 w-4 ml-2" />
                تقرير تنفيذ جديد
              </Button>
            )}
          </div>
        ) : (
          filteredExecutions.map((execution) => {
            const statusInfo = getExecutionStatus(execution)
            const StatusIcon = statusInfo.icon
            const workDuration = calculateWorkDuration(execution)

            return (
              <Card key={execution.id} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <Badge className={statusInfo.color}>
                          <StatusIcon className="h-3 w-3 ml-1" />
                          {statusInfo.status}
                        </Badge>
                        {workDuration && (
                          <Badge variant="outline">
                            <Clock className="h-3 w-3 ml-1" />
                            {workDuration}
                          </Badge>
                        )}
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                        {execution.technical_description || 'تقرير تنفيذ صيانة'}
                      </h3>
                    </div>
                    <div className="flex gap-2 ml-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onView(execution)}
                      >
                        <Eye className="h-4 w-4 ml-1" />
                        عرض
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onEdit(execution)}
                      >
                        <Edit className="h-4 w-4 ml-1" />
                        تعديل
                      </Button>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 mb-4">
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-gray-400" />
                      <div>
                        <p className="font-medium">{execution.engineer?.full_name}</p>
                        <p className="text-xs">{execution.engineer?.specialization}</p>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-gray-400" />
                      <div>
                        <p className="font-medium">{execution.service_request?.customer?.full_name}</p>
                        <p className="text-xs">{execution.service_request?.device?.device_name}</p>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-gray-400" />
                      <div>
                        <p className="font-medium">{formatDate(execution.created_at)}</p>
                        <p className="text-xs">
                          {execution.start_time ? formatTime(execution.start_time) : 'لم يبدأ'}
                        </p>
                      </div>
                    </div>
                  </div>

                  {execution.repair_actions && (
                    <div className="mb-4">
                      <p className="text-sm text-gray-700 line-clamp-2">
                        <span className="font-medium">الإجراءات:</span> {execution.repair_actions}
                      </p>
                    </div>
                  )}

                  {execution.device_status_after && (
                    <div className="mb-4">
                      <Badge variant="outline" className="bg-green-50 text-green-700">
                        <CheckCircle className="h-3 w-3 ml-1" />
                        {execution.device_status_after}
                      </Badge>
                    </div>
                  )}

                  {execution.received_by_customer && (
                    <div className="border-t pt-4">
                      <p className="text-sm text-gray-600">
                        <span className="font-medium">استلم العمل:</span> {execution.received_by_customer}
                      </p>
                    </div>
                  )}

                  {execution.future_recommendations && (
                    <div className="mt-2">
                      <div className="bg-blue-50 rounded-lg p-3">
                        <p className="text-sm text-blue-700">
                          <FileText className="h-4 w-4 inline ml-1" />
                          <span className="font-medium">توصيات:</span> {execution.future_recommendations}
                        </p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )
          })
        )}
      </div>
    </div>
  )
}
