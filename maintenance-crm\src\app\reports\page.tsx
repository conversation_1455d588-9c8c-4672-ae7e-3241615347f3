'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { DashboardOverview } from '@/components/dashboard/dashboard-overview'
import { ReportsDashboard } from '@/components/reports/reports-dashboard'
import { CustomerSatisfaction } from '@/components/follow-up/customer-satisfaction'
import { 
  ArrowRight,
  BarChart3, 
  FileText, 
  Star,
  TrendingUp,
  Users,
  Calendar,
  Target,
  Activity,
  AlertTriangle
} from 'lucide-react'
import Link from 'next/link'

type TabValue = 'overview' | 'reports' | 'satisfaction' | 'analytics'

export default function ReportsPage() {
  const [activeTab, setActiveTab] = useState<TabValue>('overview')
  const [isLoading, setIsLoading] = useState(false)

  const tabs = [
    {
      value: 'overview' as TabValue,
      label: 'لوحة المعلومات',
      icon: BarChart3,
      description: 'نظرة عامة شاملة على أداء النظام'
    },
    {
      value: 'reports' as TabValue,
      label: 'التقارير التفصيلية',
      icon: FileText,
      description: 'تقارير مفصلة قابلة للتخصيص والتصدير'
    },
    {
      value: 'satisfaction' as TabValue,
      label: 'رضا العملاء',
      icon: Star,
      description: 'متابعة تقييمات وآراء العملاء'
    },
    {
      value: 'analytics' as TabValue,
      label: 'التحليلات المتقدمة',
      icon: TrendingUp,
      description: 'تحليلات متقدمة ومؤشرات الأداء'
    }
  ]

  const quickStats = [
    {
      title: 'طلبات اليوم',
      value: '23',
      change: '+12%',
      trend: 'up',
      icon: Calendar,
      color: 'text-blue-600'
    },
    {
      title: 'معدل الإكمال',
      value: '94.5%',
      change: '+2.1%',
      trend: 'up',
      icon: Target,
      color: 'text-green-600'
    },
    {
      title: 'رضا العملاء',
      value: '4.7/5',
      change: '+0.3',
      trend: 'up',
      icon: Star,
      color: 'text-yellow-600'
    },
    {
      title: 'وقت الاستجابة',
      value: '4.2h',
      change: '-0.8h',
      trend: 'down',
      icon: Activity,
      color: 'text-purple-600'
    }
  ]

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return <DashboardOverview isLoading={isLoading} />
      case 'reports':
        return <ReportsDashboard />
      case 'satisfaction':
        return <CustomerSatisfaction />
      case 'analytics':
        return (
          <div className="text-center py-12">
            <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              التحليلات المتقدمة
            </h3>
            <p className="text-gray-500 mb-4">
              سيتم تطوير هذه الميزة قريباً مع رسوم بيانية تفاعلية متقدمة
            </p>
            <Button onClick={() => setActiveTab('reports')}>
              عرض التقارير الحالية
            </Button>
          </div>
        )
      default:
        return null
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center gap-3">
              <BarChart3 className="h-8 w-8 text-blue-600" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  المتابعة والتقارير
                </h1>
                <p className="text-gray-600">
                  لوحة معلومات شاملة وتقارير تفصيلية لأداء النظام
                </p>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <Link href="/">
                <Button variant="outline">
                  <ArrowRight className="h-4 w-4 ml-2" />
                  العودة للرئيسية
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {quickStats.map((stat, index) => {
            const IconComponent = stat.icon
            const isPositive = stat.trend === 'up'
            
            return (
              <Card key={index}>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                      <p className={`text-2xl font-bold ${stat.color}`}>{stat.value}</p>
                      <div className="flex items-center gap-1 mt-1">
                        {isPositive ? (
                          <TrendingUp className="h-3 w-3 text-green-500" />
                        ) : (
                          <TrendingUp className="h-3 w-3 text-green-500 rotate-180" />
                        )}
                        <span className={`text-xs ${isPositive ? 'text-green-600' : 'text-green-600'}`}>
                          {stat.change}
                        </span>
                      </div>
                    </div>
                    <IconComponent className={`h-8 w-8 ${stat.color}`} />
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Main Content */}
        <Card>
          <CardHeader>
            <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as TabValue)}>
              <TabsList className="grid w-full grid-cols-4">
                {tabs.map((tab) => {
                  const IconComponent = tab.icon
                  return (
                    <TabsTrigger 
                      key={tab.value} 
                      value={tab.value}
                      className="flex items-center gap-2"
                    >
                      <IconComponent className="h-4 w-4" />
                      <span className="hidden sm:inline">{tab.label}</span>
                    </TabsTrigger>
                  )
                })}
              </TabsList>
            </Tabs>
            
            <div className="mt-4">
              <CardTitle className="flex items-center gap-2">
                {tabs.find(tab => tab.value === activeTab)?.icon && (
                  <tabs.find(tab => tab.value === activeTab)!.icon className="h-5 w-5 text-blue-600" />
                )}
                {tabs.find(tab => tab.value === activeTab)?.label}
              </CardTitle>
              <CardDescription>
                {tabs.find(tab => tab.value === activeTab)?.description}
              </CardDescription>
            </div>
          </CardHeader>
          
          <CardContent>
            {renderTabContent()}
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <div className="mt-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">إجراءات سريعة</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Link href="/customers">
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardContent className="p-4 text-center">
                  <Users className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                  <h3 className="font-medium text-gray-900">إدارة العملاء</h3>
                  <p className="text-sm text-gray-500">عرض وإدارة بيانات العملاء</p>
                </CardContent>
              </Card>
            </Link>

            <Link href="/service-requests">
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardContent className="p-4 text-center">
                  <FileText className="h-8 w-8 text-green-600 mx-auto mb-2" />
                  <h3 className="font-medium text-gray-900">طلبات الصيانة</h3>
                  <p className="text-sm text-gray-500">متابعة طلبات الصيانة</p>
                </CardContent>
              </Card>
            </Link>

            <Link href="/scheduling">
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardContent className="p-4 text-center">
                  <Calendar className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                  <h3 className="font-medium text-gray-900">الجدولة</h3>
                  <p className="text-sm text-gray-500">إدارة مواعيد الصيانة</p>
                </CardContent>
              </Card>
            </Link>

            <Link href="/execution">
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardContent className="p-4 text-center">
                  <Activity className="h-8 w-8 text-orange-600 mx-auto mb-2" />
                  <h3 className="font-medium text-gray-900">التنفيذ</h3>
                  <p className="text-sm text-gray-500">تقارير تنفيذ الصيانة</p>
                </CardContent>
              </Card>
            </Link>
          </div>
        </div>

        {/* System Health */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-green-600" />
              حالة النظام
            </CardTitle>
            <CardDescription>مؤشرات صحة وأداء النظام</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Activity className="h-8 w-8 text-green-600" />
                </div>
                <h3 className="font-medium text-gray-900">أداء النظام</h3>
                <p className="text-2xl font-bold text-green-600">99.8%</p>
                <p className="text-sm text-gray-500">وقت التشغيل</p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Users className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="font-medium text-gray-900">المستخدمون النشطون</h3>
                <p className="text-2xl font-bold text-blue-600">24</p>
                <p className="text-sm text-gray-500">مستخدم متصل</p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <AlertTriangle className="h-8 w-8 text-yellow-600" />
                </div>
                <h3 className="font-medium text-gray-900">التنبيهات</h3>
                <p className="text-2xl font-bold text-yellow-600">3</p>
                <p className="text-sm text-gray-500">تنبيه نشط</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
