'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Customer, Device, ServiceRequestFormData, Priority } from '@/types'
import { Save, X, ClipboardList, Search, Plus } from 'lucide-react'
import { CustomersAPI } from '@/lib/api/customers'
import { DevicesAPI } from '@/lib/api/devices'

const serviceRequestSchema = z.object({
  customer_id: z.string().min(1, 'يجب اختيار العميل'),
  device_id: z.string().min(1, 'يجب اختيار الجهاز'),
  received_by: z.string().min(2, 'اسم مستقبل البلاغ مطلوب'),
  request_source: z.string().min(1, 'مصدر البلاغ مطلوب'),
  initial_description: z.string().min(10, 'وصف العطل مطلوب (حد أدنى 10 أحرف)'),
  priority: z.enum(['عاجل', 'متوسط', 'منخفض'], {
    errorMap: () => ({ message: 'يجب تحديد درجة الأولوية' })
  }),
})

interface ServiceRequestFormProps {
  initialData?: Partial<ServiceRequestFormData>
  onSubmit: (data: ServiceRequestFormData) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}

const REQUEST_SOURCES = [
  'هاتف',
  'بريد إلكتروني',
  'زيارة',
  'نموذج إلكتروني',
  'واتساب',
  'فاكس'
]

const PRIORITIES: { value: Priority; label: string; color: string }[] = [
  { value: 'عاجل', label: 'عاجل', color: 'bg-red-100 text-red-800' },
  { value: 'متوسط', label: 'متوسط', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'منخفض', label: 'منخفض', color: 'bg-green-100 text-green-800' },
]

export function ServiceRequestForm({ initialData, onSubmit, onCancel, isLoading }: ServiceRequestFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [customers, setCustomers] = useState<Customer[]>([])
  const [devices, setDevices] = useState<Device[]>([])
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null)
  const [customerSearch, setCustomerSearch] = useState('')
  const [isLoadingCustomers, setIsLoadingCustomers] = useState(false)
  const [isLoadingDevices, setIsLoadingDevices] = useState(false)

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    reset,
    watch,
    setValue
  } = useForm<ServiceRequestFormData>({
    resolver: zodResolver(serviceRequestSchema),
    defaultValues: initialData || {
      customer_id: '',
      device_id: '',
      received_by: '',
      request_source: '',
      initial_description: '',
      priority: 'متوسط',
    }
  })

  const watchedCustomerId = watch('customer_id')

  // Load customers on component mount
  useEffect(() => {
    loadCustomers()
  }, [])

  // Load devices when customer changes
  useEffect(() => {
    if (watchedCustomerId) {
      loadCustomerDevices(watchedCustomerId)
      const customer = customers.find(c => c.id === watchedCustomerId)
      setSelectedCustomer(customer || null)
    } else {
      setDevices([])
      setSelectedCustomer(null)
      setValue('device_id', '')
    }
  }, [watchedCustomerId, customers])

  const loadCustomers = async () => {
    setIsLoadingCustomers(true)
    const result = await CustomersAPI.getAll()
    if (result.data) {
      setCustomers(result.data)
    }
    setIsLoadingCustomers(false)
  }

  const loadCustomerDevices = async (customerId: string) => {
    setIsLoadingDevices(true)
    const result = await DevicesAPI.getByCustomerId(customerId)
    if (result.data) {
      setDevices(result.data)
    }
    setIsLoadingDevices(false)
  }

  const searchCustomers = async (query: string) => {
    if (!query.trim()) {
      loadCustomers()
      return
    }
    
    setIsLoadingCustomers(true)
    const result = await CustomersAPI.search(query)
    if (result.data) {
      setCustomers(result.data)
    }
    setIsLoadingCustomers(false)
  }

  const handleCustomerSearch = (query: string) => {
    setCustomerSearch(query)
    if (query.length >= 2) {
      searchCustomers(query)
    } else if (query.length === 0) {
      loadCustomers()
    }
  }

  const handleFormSubmit = async (data: ServiceRequestFormData) => {
    setIsSubmitting(true)
    try {
      await onSubmit(data)
      if (!initialData) {
        reset()
        setSelectedCustomer(null)
        setDevices([])
      }
    } catch (error) {
      console.error('Error submitting service request form:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ClipboardList className="h-6 w-6 text-blue-600" />
          {initialData ? 'تعديل طلب الصيانة' : 'تسجيل طلب صيانة جديد'}
        </CardTitle>
        <CardDescription>
          {initialData 
            ? 'قم بتعديل بيانات طلب الصيانة أدناه' 
            : 'أدخل بيانات طلب الصيانة الجديد أدناه'
          }
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {/* Customer Selection */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">معلومات العميل</h3>
            
            <div className="space-y-2">
              <Label htmlFor="customer_search">البحث عن العميل *</Label>
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  id="customer_search"
                  placeholder="ابحث بالاسم أو رقم الهاتف"
                  value={customerSearch}
                  onChange={(e) => handleCustomerSearch(e.target.value)}
                  className="pr-10"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>اختيار العميل *</Label>
              <Controller
                name="customer_id"
                control={control}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger className={errors.customer_id ? 'border-red-500' : ''}>
                      <SelectValue placeholder="اختر العميل" />
                    </SelectTrigger>
                    <SelectContent>
                      {isLoadingCustomers ? (
                        <SelectItem value="loading" disabled>جاري التحميل...</SelectItem>
                      ) : customers.length === 0 ? (
                        <SelectItem value="no-customers" disabled>لا توجد عملاء</SelectItem>
                      ) : (
                        customers.map((customer) => (
                          <SelectItem key={customer.id} value={customer.id}>
                            <div className="flex flex-col">
                              <span className="font-medium">{customer.full_name}</span>
                              <span className="text-sm text-gray-500">{customer.primary_phone}</span>
                            </div>
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                )}
              />
              {errors.customer_id && (
                <p className="text-sm text-red-500">{errors.customer_id.message}</p>
              )}
            </div>

            {selectedCustomer && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 mb-2">معلومات العميل المحدد</h4>
                <div className="text-sm text-blue-700 space-y-1">
                  <p><span className="font-medium">الاسم:</span> {selectedCustomer.full_name}</p>
                  <p><span className="font-medium">الهاتف:</span> {selectedCustomer.primary_phone}</p>
                  <p><span className="font-medium">العنوان:</span> {selectedCustomer.detailed_address}</p>
                  {selectedCustomer.contact_person && (
                    <p><span className="font-medium">جهة الاتصال:</span> {selectedCustomer.contact_person}</p>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Device Selection */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">معلومات الجهاز</h3>
            
            <div className="space-y-2">
              <Label>اختيار الجهاز *</Label>
              <Controller
                name="device_id"
                control={control}
                render={({ field }) => (
                  <Select 
                    value={field.value} 
                    onValueChange={field.onChange}
                    disabled={!watchedCustomerId}
                  >
                    <SelectTrigger className={errors.device_id ? 'border-red-500' : ''}>
                      <SelectValue placeholder={
                        !watchedCustomerId 
                          ? "اختر العميل أولاً" 
                          : "اختر الجهاز"
                      } />
                    </SelectTrigger>
                    <SelectContent>
                      {isLoadingDevices ? (
                        <SelectItem value="loading" disabled>جاري التحميل...</SelectItem>
                      ) : devices.length === 0 ? (
                        <SelectItem value="no-devices" disabled>
                          لا توجد أجهزة مسجلة لهذا العميل
                        </SelectItem>
                      ) : (
                        devices.map((device) => (
                          <SelectItem key={device.id} value={device.id}>
                            <div className="flex flex-col">
                              <span className="font-medium">{device.device_name} - {device.model}</span>
                              <span className="text-sm text-gray-500">الرقم التسلسلي: {device.serial_number}</span>
                            </div>
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                )}
              />
              {errors.device_id && (
                <p className="text-sm text-red-500">{errors.device_id.message}</p>
              )}
              {watchedCustomerId && devices.length === 0 && !isLoadingDevices && (
                <div className="text-sm text-gray-500 bg-gray-50 p-3 rounded">
                  لا توجد أجهزة مسجلة لهذا العميل. 
                  <Button variant="link" className="p-0 h-auto text-blue-600">
                    <Plus className="h-4 w-4 ml-1" />
                    إضافة جهاز جديد
                  </Button>
                </div>
              )}
            </div>
          </div>

          {/* Request Details */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">تفاصيل البلاغ</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="received_by">مستقبل البلاغ *</Label>
                <Input
                  id="received_by"
                  {...register('received_by')}
                  placeholder="اسم موظف خدمة العملاء"
                  className={errors.received_by ? 'border-red-500' : ''}
                />
                {errors.received_by && (
                  <p className="text-sm text-red-500">{errors.received_by.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label>مصدر البلاغ *</Label>
                <Controller
                  name="request_source"
                  control={control}
                  render={({ field }) => (
                    <Select value={field.value} onValueChange={field.onChange}>
                      <SelectTrigger className={errors.request_source ? 'border-red-500' : ''}>
                        <SelectValue placeholder="اختر مصدر البلاغ" />
                      </SelectTrigger>
                      <SelectContent>
                        {REQUEST_SOURCES.map((source) => (
                          <SelectItem key={source} value={source}>
                            {source}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
                {errors.request_source && (
                  <p className="text-sm text-red-500">{errors.request_source.message}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="initial_description">وصف العطل *</Label>
              <Textarea
                id="initial_description"
                {...register('initial_description')}
                placeholder="اكتب وصفاً مفصلاً للعطل كما ذكره العميل"
                className={errors.initial_description ? 'border-red-500' : ''}
                rows={4}
              />
              {errors.initial_description && (
                <p className="text-sm text-red-500">{errors.initial_description.message}</p>
              )}
              <p className="text-sm text-gray-500">
                اكتب وصفاً دقيقاً للمشكلة كما وصفها العميل مع ذكر أي أعراض أو أصوات غريبة
              </p>
            </div>

            <div className="space-y-2">
              <Label>درجة الأولوية *</Label>
              <Controller
                name="priority"
                control={control}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger className={errors.priority ? 'border-red-500' : ''}>
                      <SelectValue placeholder="اختر درجة الأولوية" />
                    </SelectTrigger>
                    <SelectContent>
                      {PRIORITIES.map((priority) => (
                        <SelectItem key={priority.value} value={priority.value}>
                          <div className="flex items-center gap-2">
                            <Badge className={priority.color}>
                              {priority.label}
                            </Badge>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
              {errors.priority && (
                <p className="text-sm text-red-500">{errors.priority.message}</p>
              )}
              <div className="text-sm text-gray-500 space-y-1">
                <p><span className="font-medium text-red-600">عاجل:</span> أعطال تؤثر على العمل أو الحياة اليومية</p>
                <p><span className="font-medium text-yellow-600">متوسط:</span> أعطال مهمة لكن غير حرجة</p>
                <p><span className="font-medium text-green-600">منخفض:</span> أعطال بسيطة أو صيانة دورية</p>
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex gap-4 pt-6">
            <Button
              type="submit"
              disabled={isSubmitting || isLoading}
              className="flex-1"
            >
              <Save className="h-4 w-4 ml-2" />
              {isSubmitting ? 'جاري الحفظ...' : 'تسجيل الطلب'}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting || isLoading}
            >
              <X className="h-4 w-4 ml-2" />
              إلغاء
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
