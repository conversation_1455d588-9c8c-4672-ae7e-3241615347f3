#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('\n🔧 إصلاح مشاكل النظام...\n');

// إصلاح ملف supabase.ts
const supabaseContent = `import { createClient } from '@supabase/supabase-js'
import { Database } from '@/types/database'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://demo.supabase.co'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'demo-key'

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey)
`;

// إصلاح ملف utils.ts
const utilsContent = `import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(date: string | Date) {
  const d = new Date(date)
  return d.toLocaleDateString('ar-SA', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

export function formatDateTime(date: string | Date) {
  const d = new Date(date)
  return d.toLocaleString('ar-SA', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

export function formatTime(date: string | Date) {
  const d = new Date(date)
  return d.toLocaleTimeString('ar-SA', {
    hour: '2-digit',
    minute: '2-digit'
  })
}
`;

// إصلاح ملف types/index.ts
const typesContent = `// تعريف الأنواع الأساسية للنظام

export interface Customer {
  id: string
  full_name: string
  primary_phone: string
  secondary_phone?: string | null
  email?: string | null
  detailed_address: string
  landmark?: string | null
  contact_person?: string | null
  special_notes?: string | null
  created_at: string
  updated_at: string
  devices?: Device[]
}

export interface Device {
  id: string
  customer_id: string
  device_name: string
  model: string
  serial_number: string
  purchase_date?: string | null
  warranty_end_date?: string | null
  original_invoice_number?: string | null
  created_at: string
  updated_at: string
  customer?: Customer
}

export interface ServiceRequest {
  id: string
  customer_id: string
  device_id: string
  request_date: string
  received_by: string
  request_source: string
  initial_description: string
  priority: 'عاجل' | 'متوسط' | 'منخفض'
  status: 'قيد المراجعة' | 'تم تسجيله' | 'في انتظار فني' | 'تم تحديد موعد' | 'في الطريق للعميل' | 'قيد التنفيذ' | 'مكتمل' | 'مغلق'
  created_at: string
  updated_at: string
  customer?: Customer
  device?: Device
}

export interface Engineer {
  id: string
  full_name: string
  phone: string
  email?: string | null
  specialization?: string | null
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface Scheduling {
  id: string
  service_request_id: string
  engineer_id: string
  scheduled_date: string
  estimated_duration: number
  engineer_notes?: string | null
  status: string
  created_at: string
  updated_at: string
  engineer?: Engineer
  service_request?: ServiceRequest
}

export interface Execution {
  id: string
  service_request_id: string
  engineer_id: string
  arrival_time?: string | null
  start_time?: string | null
  end_time?: string | null
  technical_description: string
  repair_actions: string
  failure_cause?: string | null
  tools_used?: string | null
  future_recommendations?: string | null
  received_by_customer?: string | null
  device_status_after: string
  customer_signature?: string | null
  technical_report?: string | null
  created_at: string
  updated_at: string
  engineer?: Engineer
  service_request?: ServiceRequest
}

export interface Part {
  id: string
  part_name: string
  part_number: string
  description?: string | null
  unit_price: number
  stock_quantity: number
  created_at: string
  updated_at: string
}

export interface ExecutionPart {
  id: string
  execution_id: string
  part_id: string
  quantity_used: number
  unit_price_at_time: number
  created_at: string
  part?: Part
}

export interface ApiResponse<T> {
  data?: T
  error?: string
  message?: string
}
`;

// كتابة الملفات
try {
  // إنشاء المجلدات إذا لم تكن موجودة
  const dirs = [
    'src/lib',
    'src/types',
    'src/components/ui'
  ];
  
  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`✅ تم إنشاء مجلد: ${dir}`);
    }
  });

  // كتابة الملفات
  fs.writeFileSync('src/lib/supabase.ts', supabaseContent);
  console.log('✅ تم إصلاح ملف supabase.ts');

  fs.writeFileSync('src/lib/utils.ts', utilsContent);
  console.log('✅ تم إصلاح ملف utils.ts');

  fs.writeFileSync('src/types/index.ts', typesContent);
  console.log('✅ تم إصلاح ملف types/index.ts');

  // إنشاء ملف globals.css إذا لم يكن موجوداً
  const globalsPath = 'src/app/globals.css';
  if (!fs.existsSync(globalsPath)) {
    const globalsContent = `@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    direction: rtl;
  }
}
`;
    fs.writeFileSync(globalsPath, globalsContent);
    console.log('✅ تم إنشاء ملف globals.css');
  }

  console.log('\n🎉 تم إصلاح جميع المشاكل بنجاح!');
  console.log('\nالآن يمكنك تشغيل النظام:');
  console.log('npm run dev');

} catch (error) {
  console.error('❌ خطأ في الإصلاح:', error.message);
  process.exit(1);
}
