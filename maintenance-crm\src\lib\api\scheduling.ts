import { supabase } from '@/lib/supabase'
import { Scheduling, ApiResponse } from '@/types'

interface SchedulingFormData {
  service_request_id: string
  engineer_id: string
  scheduled_date: string
  estimated_duration: number
  engineer_notes?: string
  status?: string
}

export class SchedulingAPI {
  // Get all schedulings with related data
  static async getAll(): Promise<ApiResponse<Scheduling[]>> {
    try {
      const { data, error } = await supabase
        .from('scheduling')
        .select(`
          *,
          engineers(id, full_name, phone, email, specialization),
          service_requests(
            id, 
            initial_description, 
            priority, 
            status,
            customers(id, full_name, primary_phone, detailed_address),
            devices(id, device_name, model, serial_number)
          )
        `)
        .order('scheduled_date', { ascending: true })

      if (error) {
        console.error('Error fetching schedulings:', error)
        return { error: 'فشل في جلب بيانات الجدولة' }
      }

      return { data: data || [] }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Get scheduling by ID
  static async getById(id: string): Promise<ApiResponse<Scheduling>> {
    try {
      const { data, error } = await supabase
        .from('scheduling')
        .select(`
          *,
          engineers(id, full_name, phone, email, specialization),
          service_requests(
            id, 
            initial_description, 
            priority, 
            status,
            customers(id, full_name, primary_phone, detailed_address, contact_person),
            devices(id, device_name, model, serial_number)
          )
        `)
        .eq('id', id)
        .single()

      if (error) {
        console.error('Error fetching scheduling:', error)
        return { error: 'فشل في جلب بيانات الجدولة' }
      }

      if (!data) {
        return { error: 'الجدولة غير موجودة' }
      }

      return { data }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Get schedulings by engineer ID
  static async getByEngineerId(engineerId: string): Promise<ApiResponse<Scheduling[]>> {
    try {
      const { data, error } = await supabase
        .from('scheduling')
        .select(`
          *,
          service_requests(
            id, 
            initial_description, 
            priority, 
            status,
            customers(id, full_name, primary_phone, detailed_address),
            devices(id, device_name, model, serial_number)
          )
        `)
        .eq('engineer_id', engineerId)
        .order('scheduled_date', { ascending: true })

      if (error) {
        console.error('Error fetching engineer schedulings:', error)
        return { error: 'فشل في جلب جدولة المهندس' }
      }

      return { data: data || [] }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Get schedulings by date range
  static async getByDateRange(startDate: string, endDate: string): Promise<ApiResponse<Scheduling[]>> {
    try {
      const { data, error } = await supabase
        .from('scheduling')
        .select(`
          *,
          engineers(id, full_name, phone, specialization),
          service_requests(
            id, 
            initial_description, 
            priority, 
            status,
            customers(id, full_name, primary_phone),
            devices(id, device_name, model)
          )
        `)
        .gte('scheduled_date', startDate)
        .lte('scheduled_date', endDate)
        .order('scheduled_date', { ascending: true })

      if (error) {
        console.error('Error fetching schedulings by date range:', error)
        return { error: 'فشل في جلب الجدولة للفترة المحددة' }
      }

      return { data: data || [] }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Create new scheduling
  static async create(schedulingData: SchedulingFormData): Promise<ApiResponse<Scheduling>> {
    try {
      // Verify service request exists and is not already scheduled
      const { data: serviceRequest } = await supabase
        .from('service_requests')
        .select('id, status')
        .eq('id', schedulingData.service_request_id)
        .single()

      if (!serviceRequest) {
        return { error: 'طلب الصيانة غير موجود' }
      }

      // Check if service request is already scheduled
      const { data: existingScheduling } = await supabase
        .from('scheduling')
        .select('id')
        .eq('service_request_id', schedulingData.service_request_id)
        .single()

      if (existingScheduling) {
        return { error: 'طلب الصيانة مجدول مسبقاً' }
      }

      // Verify engineer exists and is active
      const { data: engineer } = await supabase
        .from('engineers')
        .select('id, is_active')
        .eq('id', schedulingData.engineer_id)
        .single()

      if (!engineer) {
        return { error: 'المهندس غير موجود' }
      }

      if (!engineer.is_active) {
        return { error: 'المهندس غير نشط' }
      }

      // Check for scheduling conflicts
      const scheduledDate = new Date(schedulingData.scheduled_date)
      const endTime = new Date(scheduledDate.getTime() + schedulingData.estimated_duration * 60000)
      
      const { data: conflicts } = await supabase
        .from('scheduling')
        .select('id, scheduled_date, estimated_duration')
        .eq('engineer_id', schedulingData.engineer_id)
        .gte('scheduled_date', scheduledDate.toISOString())
        .lt('scheduled_date', endTime.toISOString())
        .neq('status', 'ملغي')

      if (conflicts && conflicts.length > 0) {
        return { error: 'يوجد تعارض في جدولة المهندس في هذا الوقت' }
      }

      const { data, error } = await supabase
        .from('scheduling')
        .insert([{
          ...schedulingData,
          status: schedulingData.status || 'مجدول'
        }])
        .select(`
          *,
          engineers(id, full_name, phone, email, specialization),
          service_requests(
            id, 
            initial_description, 
            priority, 
            status,
            customers(id, full_name, primary_phone, detailed_address),
            devices(id, device_name, model, serial_number)
          )
        `)
        .single()

      if (error) {
        console.error('Error creating scheduling:', error)
        return { error: 'فشل في إنشاء الجدولة' }
      }

      // Update service request status
      await supabase
        .from('service_requests')
        .update({ status: 'تم تحديد موعد' })
        .eq('id', schedulingData.service_request_id)

      return { 
        data, 
        message: 'تم إنشاء الجدولة بنجاح' 
      }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Update scheduling
  static async update(id: string, schedulingData: Partial<SchedulingFormData>): Promise<ApiResponse<Scheduling>> {
    try {
      // If updating engineer or date, check for conflicts
      if (schedulingData.engineer_id || schedulingData.scheduled_date || schedulingData.estimated_duration) {
        const currentScheduling = await this.getById(id)
        if (currentScheduling.error || !currentScheduling.data) {
          return { error: 'الجدولة غير موجودة' }
        }

        const engineerId = schedulingData.engineer_id || currentScheduling.data.engineer_id
        const scheduledDate = schedulingData.scheduled_date || currentScheduling.data.scheduled_date
        const duration = schedulingData.estimated_duration || currentScheduling.data.estimated_duration

        const startTime = new Date(scheduledDate)
        const endTime = new Date(startTime.getTime() + duration * 60000)
        
        const { data: conflicts } = await supabase
          .from('scheduling')
          .select('id')
          .eq('engineer_id', engineerId)
          .gte('scheduled_date', startTime.toISOString())
          .lt('scheduled_date', endTime.toISOString())
          .neq('id', id)
          .neq('status', 'ملغي')

        if (conflicts && conflicts.length > 0) {
          return { error: 'يوجد تعارض في جدولة المهندس في هذا الوقت' }
        }
      }

      const { data, error } = await supabase
        .from('scheduling')
        .update(schedulingData)
        .eq('id', id)
        .select(`
          *,
          engineers(id, full_name, phone, email, specialization),
          service_requests(
            id, 
            initial_description, 
            priority, 
            status,
            customers(id, full_name, primary_phone, detailed_address),
            devices(id, device_name, model, serial_number)
          )
        `)
        .single()

      if (error) {
        console.error('Error updating scheduling:', error)
        return { error: 'فشل في تحديث الجدولة' }
      }

      return { 
        data, 
        message: 'تم تحديث الجدولة بنجاح' 
      }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Update scheduling status
  static async updateStatus(id: string, status: string): Promise<ApiResponse<Scheduling>> {
    try {
      const { data, error } = await supabase
        .from('scheduling')
        .update({ status })
        .eq('id', id)
        .select(`
          *,
          engineers(id, full_name, phone),
          service_requests(id, initial_description, status)
        `)
        .single()

      if (error) {
        console.error('Error updating scheduling status:', error)
        return { error: 'فشل في تحديث حالة الجدولة' }
      }

      // Update service request status based on scheduling status
      if (data.service_request_id) {
        let serviceRequestStatus = data.service_requests?.status
        
        switch (status) {
          case 'في الطريق':
            serviceRequestStatus = 'في الطريق للعميل'
            break
          case 'وصل':
          case 'مكتمل':
            serviceRequestStatus = 'قيد التنفيذ'
            break
          case 'ملغي':
            serviceRequestStatus = 'في انتظار فني'
            break
        }

        if (serviceRequestStatus && serviceRequestStatus !== data.service_requests?.status) {
          await supabase
            .from('service_requests')
            .update({ status: serviceRequestStatus })
            .eq('id', data.service_request_id)
        }
      }

      return { 
        data, 
        message: 'تم تحديث حالة الجدولة بنجاح' 
      }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Delete scheduling
  static async delete(id: string): Promise<ApiResponse<void>> {
    try {
      // Get scheduling details before deletion
      const schedulingResult = await this.getById(id)
      if (schedulingResult.error || !schedulingResult.data) {
        return { error: 'الجدولة غير موجودة' }
      }

      const { error } = await supabase
        .from('scheduling')
        .delete()
        .eq('id', id)

      if (error) {
        console.error('Error deleting scheduling:', error)
        return { error: 'فشل في حذف الجدولة' }
      }

      // Update service request status back to waiting for engineer
      if (schedulingResult.data.service_request_id) {
        await supabase
          .from('service_requests')
          .update({ status: 'في انتظار فني' })
          .eq('id', schedulingResult.data.service_request_id)
      }

      return { message: 'تم حذف الجدولة بنجاح' }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Get today's schedulings
  static async getTodaySchedulings(): Promise<ApiResponse<Scheduling[]>> {
    try {
      const today = new Date()
      const startOfDay = new Date(today.setHours(0, 0, 0, 0)).toISOString()
      const endOfDay = new Date(today.setHours(23, 59, 59, 999)).toISOString()

      return this.getByDateRange(startOfDay, endOfDay)
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Get overdue schedulings
  static async getOverdueSchedulings(): Promise<ApiResponse<Scheduling[]>> {
    try {
      const now = new Date().toISOString()

      const { data, error } = await supabase
        .from('scheduling')
        .select(`
          *,
          engineers(id, full_name, phone),
          service_requests(
            id, 
            initial_description, 
            priority,
            customers(id, full_name, primary_phone)
          )
        `)
        .lt('scheduled_date', now)
        .not('status', 'in', '(مكتمل,ملغي)')
        .order('scheduled_date', { ascending: true })

      if (error) {
        console.error('Error fetching overdue schedulings:', error)
        return { error: 'فشل في جلب الجدولات المتأخرة' }
      }

      return { data: data || [] }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }
}
