@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    نظام إدارة علاقات العملاء - الصيانة
echo ========================================
echo.

REM التحقق من وجود Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: Node.js غير مثبت
    echo يرجى تثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)

echo ✅ Node.js مثبت
node --version

REM التحقق من وجود ملف البيئة
if not exist ".env.local" (
    echo.
    echo ⚠️  تحذير: ملف .env.local غير موجود
    echo يرجى إنشاء ملف .env.local وإضافة بيانات Supabase
    echo راجع ملف SETUP.md للتفاصيل
    echo.
    pause
)

REM التحقق من وجود node_modules
if not exist "node_modules" (
    echo.
    echo 📦 تثبيت التبعيات...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت التبعيات
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت التبعيات بنجاح
)

echo.
echo 🚀 بدء تشغيل النظام...
echo.
echo سيتم فتح النظام في المتصفح على العنوان:
echo http://localhost:3000
echo.
echo للإيقاف: اضغط Ctrl+C
echo.

REM تشغيل النظام
npm run dev

pause
