import { supabase } from '@/lib/supabase'
import { ServiceRequest, ServiceRequestFormData, ApiResponse, Priority, RequestStatus } from '@/types'

export class ServiceRequestsAPI {
  // Get all service requests with related data
  static async getAll(): Promise<ApiResponse<ServiceRequest[]>> {
    try {
      const { data, error } = await supabase
        .from('service_requests')
        .select(`
          *,
          customers(id, full_name, primary_phone, detailed_address),
          devices(id, device_name, model, serial_number)
        `)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching service requests:', error)
        return { error: 'فشل في جلب طلبات الصيانة' }
      }

      return { data: data || [] }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Get service request by ID
  static async getById(id: string): Promise<ApiResponse<ServiceRequest>> {
    try {
      const { data, error } = await supabase
        .from('service_requests')
        .select(`
          *,
          customers(id, full_name, primary_phone, detailed_address, contact_person),
          devices(id, device_name, model, serial_number, warranty_end_date)
        `)
        .eq('id', id)
        .single()

      if (error) {
        console.error('Error fetching service request:', error)
        return { error: 'فشل في جلب طلب الصيانة' }
      }

      if (!data) {
        return { error: 'طلب الصيانة غير موجود' }
      }

      return { data }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Get service requests by customer ID
  static async getByCustomerId(customerId: string): Promise<ApiResponse<ServiceRequest[]>> {
    try {
      const { data, error } = await supabase
        .from('service_requests')
        .select(`
          *,
          devices(id, device_name, model, serial_number)
        `)
        .eq('customer_id', customerId)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching customer service requests:', error)
        return { error: 'فشل في جلب طلبات صيانة العميل' }
      }

      return { data: data || [] }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Create new service request
  static async create(requestData: ServiceRequestFormData): Promise<ApiResponse<ServiceRequest>> {
    try {
      // Verify customer and device exist
      const { data: customer } = await supabase
        .from('customers')
        .select('id')
        .eq('id', requestData.customer_id)
        .single()

      if (!customer) {
        return { error: 'العميل غير موجود' }
      }

      const { data: device } = await supabase
        .from('devices')
        .select('id, customer_id')
        .eq('id', requestData.device_id)
        .single()

      if (!device) {
        return { error: 'الجهاز غير موجود' }
      }

      if (device.customer_id !== requestData.customer_id) {
        return { error: 'الجهاز لا ينتمي للعميل المحدد' }
      }

      const { data, error } = await supabase
        .from('service_requests')
        .insert([{
          ...requestData,
          status: 'قيد المراجعة' as RequestStatus
        }])
        .select(`
          *,
          customers(id, full_name, primary_phone, detailed_address),
          devices(id, device_name, model, serial_number)
        `)
        .single()

      if (error) {
        console.error('Error creating service request:', error)
        return { error: 'فشل في إنشاء طلب الصيانة' }
      }

      return { 
        data, 
        message: 'تم إنشاء طلب الصيانة بنجاح' 
      }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Update service request
  static async update(id: string, requestData: Partial<ServiceRequestFormData>): Promise<ApiResponse<ServiceRequest>> {
    try {
      // If updating customer or device, verify they exist and are related
      if (requestData.customer_id || requestData.device_id) {
        const currentRequest = await this.getById(id)
        if (currentRequest.error || !currentRequest.data) {
          return { error: 'طلب الصيانة غير موجود' }
        }

        const customerId = requestData.customer_id || currentRequest.data.customer_id
        const deviceId = requestData.device_id || currentRequest.data.device_id

        const { data: device } = await supabase
          .from('devices')
          .select('id, customer_id')
          .eq('id', deviceId)
          .single()

        if (!device || device.customer_id !== customerId) {
          return { error: 'الجهاز لا ينتمي للعميل المحدد' }
        }
      }

      const { data, error } = await supabase
        .from('service_requests')
        .update(requestData)
        .eq('id', id)
        .select(`
          *,
          customers(id, full_name, primary_phone, detailed_address),
          devices(id, device_name, model, serial_number)
        `)
        .single()

      if (error) {
        console.error('Error updating service request:', error)
        return { error: 'فشل في تحديث طلب الصيانة' }
      }

      return { 
        data, 
        message: 'تم تحديث طلب الصيانة بنجاح' 
      }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Update service request status
  static async updateStatus(id: string, status: RequestStatus): Promise<ApiResponse<ServiceRequest>> {
    try {
      const { data, error } = await supabase
        .from('service_requests')
        .update({ status })
        .eq('id', id)
        .select(`
          *,
          customers(id, full_name, primary_phone),
          devices(id, device_name, model)
        `)
        .single()

      if (error) {
        console.error('Error updating service request status:', error)
        return { error: 'فشل في تحديث حالة طلب الصيانة' }
      }

      return { 
        data, 
        message: 'تم تحديث حالة طلب الصيانة بنجاح' 
      }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Delete service request
  static async delete(id: string): Promise<ApiResponse<void>> {
    try {
      // Check if request has scheduling, execution, or follow-up records
      const { data: scheduling } = await supabase
        .from('scheduling')
        .select('id')
        .eq('service_request_id', id)
        .limit(1)

      if (scheduling && scheduling.length > 0) {
        return { error: 'لا يمكن حذف طلب الصيانة لأنه يحتوي على جدولة' }
      }

      const { error } = await supabase
        .from('service_requests')
        .delete()
        .eq('id', id)

      if (error) {
        console.error('Error deleting service request:', error)
        return { error: 'فشل في حذف طلب الصيانة' }
      }

      return { message: 'تم حذف طلب الصيانة بنجاح' }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Search service requests
  static async search(query: string): Promise<ApiResponse<ServiceRequest[]>> {
    try {
      const { data, error } = await supabase
        .from('service_requests')
        .select(`
          *,
          customers(id, full_name, primary_phone),
          devices(id, device_name, model, serial_number)
        `)
        .or(`initial_description.ilike.%${query}%,received_by.ilike.%${query}%`)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error searching service requests:', error)
        return { error: 'فشل في البحث عن طلبات الصيانة' }
      }

      return { data: data || [] }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Get service request statistics
  static async getStatistics(): Promise<ApiResponse<{
    total: number
    byStatus: Record<RequestStatus, number>
    byPriority: Record<Priority, number>
    recentRequests: number
  }>> {
    try {
      const { data: allRequests, error } = await supabase
        .from('service_requests')
        .select('status, priority, created_at')

      if (error) {
        console.error('Error fetching service request statistics:', error)
        return { error: 'فشل في جلب إحصائيات طلبات الصيانة' }
      }

      const requests = allRequests || []
      
      // Count by status
      const byStatus = requests.reduce((acc, request) => {
        acc[request.status as RequestStatus] = (acc[request.status as RequestStatus] || 0) + 1
        return acc
      }, {} as Record<RequestStatus, number>)

      // Count by priority
      const byPriority = requests.reduce((acc, request) => {
        acc[request.priority as Priority] = (acc[request.priority as Priority] || 0) + 1
        return acc
      }, {} as Record<Priority, number>)

      // Count recent requests (last 7 days)
      const sevenDaysAgo = new Date()
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
      
      const recentRequests = requests.filter(request =>
        new Date(request.created_at) > sevenDaysAgo
      ).length

      return {
        data: {
          total: requests.length,
          byStatus,
          byPriority,
          recentRequests
        }
      }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Get requests by status
  static async getByStatus(status: RequestStatus): Promise<ApiResponse<ServiceRequest[]>> {
    try {
      const { data, error } = await supabase
        .from('service_requests')
        .select(`
          *,
          customers(id, full_name, primary_phone),
          devices(id, device_name, model, serial_number)
        `)
        .eq('status', status)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching service requests by status:', error)
        return { error: 'فشل في جلب طلبات الصيانة' }
      }

      return { data: data || [] }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Get requests by priority
  static async getByPriority(priority: Priority): Promise<ApiResponse<ServiceRequest[]>> {
    try {
      const { data, error } = await supabase
        .from('service_requests')
        .select(`
          *,
          customers(id, full_name, primary_phone),
          devices(id, device_name, model, serial_number)
        `)
        .eq('priority', priority)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching service requests by priority:', error)
        return { error: 'فشل في جلب طلبات الصيانة' }
      }

      return { data: data || [] }
    } catch (error) {
      console.error('Unexpected error:', error)
      return { error: 'حدث خطأ غير متوقع' }
    }
  }
}
