'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { 
  FileText, 
  Download, 
  Calendar,
  TrendingUp,
  Users,
  DollarSign,
  Clock,
  Target,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  LineChart,
  Filter
} from 'lucide-react'

interface ReportFilters {
  dateRange: string
  reportType: string
  customStartDate: string
  customEndDate: string
  engineer: string
  customer: string
}

interface ReportData {
  title: string
  description: string
  icon: any
  data: any
  chartType: 'bar' | 'pie' | 'line'
}

export function ReportsDashboard() {
  const [filters, setFilters] = useState<ReportFilters>({
    dateRange: 'thisMonth',
    reportType: 'overview',
    customStartDate: '',
    customEndDate: '',
    engineer: 'all',
    customer: 'all'
  })
  const [isGenerating, setIsGenerating] = useState(false)
  const [reports, setReports] = useState<ReportData[]>([])

  const reportTypes = [
    { value: 'overview', label: 'تقرير عام شامل' },
    { value: 'performance', label: 'تقرير الأداء' },
    { value: 'financial', label: 'التقرير المالي' },
    { value: 'customer', label: 'تقرير العملاء' },
    { value: 'engineer', label: 'تقرير المهندسين' },
    { value: 'parts', label: 'تقرير قطع الغيار' },
    { value: 'satisfaction', label: 'تقرير رضا العملاء' }
  ]

  const dateRanges = [
    { value: 'today', label: 'اليوم' },
    { value: 'thisWeek', label: 'هذا الأسبوع' },
    { value: 'thisMonth', label: 'هذا الشهر' },
    { value: 'lastMonth', label: 'الشهر الماضي' },
    { value: 'thisQuarter', label: 'هذا الربع' },
    { value: 'thisYear', label: 'هذا العام' },
    { value: 'custom', label: 'فترة مخصصة' }
  ]

  const generateReport = async () => {
    setIsGenerating(true)
    
    // محاكاة توليد التقارير
    setTimeout(() => {
      const mockReports: ReportData[] = [
        {
          title: 'إحصائيات الأداء العامة',
          description: 'ملخص شامل لأداء النظام خلال الفترة المحددة',
          icon: TrendingUp,
          chartType: 'bar',
          data: {
            totalRequests: 156,
            completedRequests: 142,
            averageResponseTime: 4.2,
            customerSatisfaction: 4.7,
            revenue: 125430
          }
        },
        {
          title: 'توزيع طلبات الصيانة',
          description: 'توزيع الطلبات حسب النوع والأولوية',
          icon: PieChart,
          chartType: 'pie',
          data: {
            urgent: 23,
            medium: 89,
            low: 44,
            completed: 142,
            pending: 14
          }
        },
        {
          title: 'اتجاه الإيرادات',
          description: 'تطور الإيرادات خلال الأشهر الماضية',
          icon: LineChart,
          chartType: 'line',
          data: {
            months: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
            revenue: [95000, 108000, 112000, 125000, 118000, 125430]
          }
        }
      ]
      
      setReports(mockReports)
      setIsGenerating(false)
    }, 2000)
  }

  const exportReport = (format: 'pdf' | 'excel' | 'csv') => {
    // محاكاة تصدير التقرير
    alert(`سيتم تصدير التقرير بصيغة ${format.toUpperCase()}`)
  }

  const getReportTypeIcon = (type: string) => {
    const icons: Record<string, any> = {
      overview: BarChart3,
      performance: TrendingUp,
      financial: DollarSign,
      customer: Users,
      engineer: Users,
      parts: FileText,
      satisfaction: Target
    }
    return icons[type] || FileText
  }

  return (
    <div className="space-y-8">
      {/* Report Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5 text-blue-600" />
            مرشحات التقرير
          </CardTitle>
          <CardDescription>
            حدد نوع التقرير والفترة الزمنية المطلوبة
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label>نوع التقرير</Label>
              <Select
                value={filters.reportType}
                onValueChange={(value) => setFilters(prev => ({ ...prev, reportType: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {reportTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>الفترة الزمنية</Label>
              <Select
                value={filters.dateRange}
                onValueChange={(value) => setFilters(prev => ({ ...prev, dateRange: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {dateRanges.map((range) => (
                    <SelectItem key={range.value} value={range.value}>
                      {range.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {filters.dateRange === 'custom' && (
              <>
                <div className="space-y-2">
                  <Label>من تاريخ</Label>
                  <Input
                    type="date"
                    value={filters.customStartDate}
                    onChange={(e) => setFilters(prev => ({ ...prev, customStartDate: e.target.value }))}
                  />
                </div>

                <div className="space-y-2">
                  <Label>إلى تاريخ</Label>
                  <Input
                    type="date"
                    value={filters.customEndDate}
                    onChange={(e) => setFilters(prev => ({ ...prev, customEndDate: e.target.value }))}
                  />
                </div>
              </>
            )}
          </div>

          <div className="flex gap-4 mt-6">
            <Button 
              onClick={generateReport}
              disabled={isGenerating}
              className="flex items-center gap-2"
            >
              <FileText className="h-4 w-4" />
              {isGenerating ? 'جاري التوليد...' : 'توليد التقرير'}
            </Button>
            
            {reports.length > 0 && (
              <div className="flex gap-2">
                <Button 
                  variant="outline" 
                  onClick={() => exportReport('pdf')}
                  className="flex items-center gap-2"
                >
                  <Download className="h-4 w-4" />
                  PDF
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => exportReport('excel')}
                  className="flex items-center gap-2"
                >
                  <Download className="h-4 w-4" />
                  Excel
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => exportReport('csv')}
                  className="flex items-center gap-2"
                >
                  <Download className="h-4 w-4" />
                  CSV
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Report Results */}
      {isGenerating && (
        <Card>
          <CardContent className="p-8">
            <div className="flex items-center justify-center space-x-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="text-lg">جاري توليد التقرير...</span>
            </div>
          </CardContent>
        </Card>
      )}

      {reports.length > 0 && !isGenerating && (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-gray-900">نتائج التقرير</h2>
            <Badge variant="outline" className="flex items-center gap-2">
              <Calendar className="h-3 w-3" />
              {dateRanges.find(r => r.value === filters.dateRange)?.label}
            </Badge>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {reports.map((report, index) => {
              const IconComponent = report.icon
              
              return (
                <Card key={index}>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <IconComponent className="h-5 w-5 text-blue-600" />
                      {report.title}
                    </CardTitle>
                    <CardDescription>{report.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    {report.chartType === 'bar' && (
                      <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="text-center">
                            <div className="text-2xl font-bold text-blue-600">
                              {report.data.totalRequests}
                            </div>
                            <div className="text-sm text-gray-500">إجمالي الطلبات</div>
                          </div>
                          <div className="text-center">
                            <div className="text-2xl font-bold text-green-600">
                              {report.data.completedRequests}
                            </div>
                            <div className="text-sm text-gray-500">طلبات مكتملة</div>
                          </div>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          <div className="text-center">
                            <div className="text-2xl font-bold text-orange-600">
                              {report.data.averageResponseTime}h
                            </div>
                            <div className="text-sm text-gray-500">متوسط وقت الاستجابة</div>
                          </div>
                          <div className="text-center">
                            <div className="text-2xl font-bold text-purple-600">
                              {report.data.customerSatisfaction}/5
                            </div>
                            <div className="text-sm text-gray-500">رضا العملاء</div>
                          </div>
                        </div>
                        <div className="text-center bg-green-50 rounded-lg p-4">
                          <div className="text-3xl font-bold text-green-600">
                            {report.data.revenue.toLocaleString()} ريال
                          </div>
                          <div className="text-sm text-green-700">إجمالي الإيرادات</div>
                        </div>
                      </div>
                    )}

                    {report.chartType === 'pie' && (
                      <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="flex items-center justify-between p-3 bg-red-50 rounded">
                            <span className="text-sm">عاجل</span>
                            <Badge className="bg-red-100 text-red-800">{report.data.urgent}</Badge>
                          </div>
                          <div className="flex items-center justify-between p-3 bg-yellow-50 rounded">
                            <span className="text-sm">متوسط</span>
                            <Badge className="bg-yellow-100 text-yellow-800">{report.data.medium}</Badge>
                          </div>
                          <div className="flex items-center justify-between p-3 bg-green-50 rounded">
                            <span className="text-sm">منخفض</span>
                            <Badge className="bg-green-100 text-green-800">{report.data.low}</Badge>
                          </div>
                          <div className="flex items-center justify-between p-3 bg-blue-50 rounded">
                            <span className="text-sm">معلق</span>
                            <Badge className="bg-blue-100 text-blue-800">{report.data.pending}</Badge>
                          </div>
                        </div>
                        <div className="text-center bg-gray-50 rounded-lg p-4">
                          <div className="text-2xl font-bold text-gray-900">
                            {((report.data.completed / (report.data.completed + report.data.pending)) * 100).toFixed(1)}%
                          </div>
                          <div className="text-sm text-gray-600">معدل الإكمال</div>
                        </div>
                      </div>
                    )}

                    {report.chartType === 'line' && (
                      <div className="space-y-4">
                        <div className="grid grid-cols-3 gap-2 text-xs">
                          {report.data.months.map((month: string, idx: number) => (
                            <div key={idx} className="text-center p-2 bg-gray-50 rounded">
                              <div className="font-medium">{month}</div>
                              <div className="text-blue-600">
                                {report.data.revenue[idx].toLocaleString()}
                              </div>
                            </div>
                          ))}
                        </div>
                        <div className="text-center bg-blue-50 rounded-lg p-4">
                          <div className="text-2xl font-bold text-blue-600">
                            +{(((report.data.revenue[5] - report.data.revenue[0]) / report.data.revenue[0]) * 100).toFixed(1)}%
                          </div>
                          <div className="text-sm text-blue-700">نمو الإيرادات</div>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      )}

      {/* Quick Reports */}
      <Card>
        <CardHeader>
          <CardTitle>التقارير السريعة</CardTitle>
          <CardDescription>تقارير جاهزة يمكن توليدها بنقرة واحدة</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {reportTypes.map((type) => {
              const IconComponent = getReportTypeIcon(type.value)
              
              return (
                <Button
                  key={type.value}
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center gap-2"
                  onClick={() => {
                    setFilters(prev => ({ ...prev, reportType: type.value }))
                    generateReport()
                  }}
                >
                  <IconComponent className="h-6 w-6 text-blue-600" />
                  <span className="text-sm text-center">{type.label}</span>
                </Button>
              )
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
