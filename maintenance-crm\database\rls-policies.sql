-- Enable Row Level Security (RLS) on all tables
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE devices ENABLE ROW LEVEL SECURITY;
ALTER TABLE engineers ENABLE ROW LEVEL SECURITY;
ALTER TABLE service_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE scheduling ENABLE ROW LEVEL SECURITY;
ALTER TABLE parts ENABLE ROW LEVEL SECURITY;
ALTER TABLE execution ENABLE ROW LEVEL SECURITY;
ALTER TABLE execution_parts ENABLE ROW LEVEL SECURITY;
ALTER TABLE follow_up ENABLE ROW LEVEL SECURITY;
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;

-- Create user roles
CREATE ROLE admin_role;
CREATE ROLE engineer_role;
CREATE ROLE customer_service_role;
CREATE ROLE viewer_role;

-- Grant permissions to admin role (full access)
GRANT ALL ON ALL TABLES IN SCHEMA public TO admin_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO admin_role;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO admin_role;

-- Grant permissions to engineer role (limited access)
GRANT SELECT, UPDATE ON customers, devices, service_requests, scheduling, execution, execution_parts, parts TO engineer_role;
GRANT INSERT ON execution, execution_parts TO engineer_role;

-- Grant permissions to customer service role
GRANT SELECT, INSERT, UPDATE ON customers, devices, service_requests, scheduling, follow_up TO customer_service_role;
GRANT SELECT ON engineers, parts, execution, invoices TO customer_service_role;

-- Grant permissions to viewer role (read-only)
GRANT SELECT ON ALL TABLES IN SCHEMA public TO viewer_role;

-- RLS Policies for customers table
CREATE POLICY "Admin can do everything on customers" ON customers
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Customer service can manage customers" ON customers
    FOR ALL USING (auth.jwt() ->> 'role' IN ('admin', 'customer_service'));

CREATE POLICY "Engineers can view customers" ON customers
    FOR SELECT USING (auth.jwt() ->> 'role' IN ('admin', 'customer_service', 'engineer'));

CREATE POLICY "Viewers can view customers" ON customers
    FOR SELECT USING (auth.jwt() ->> 'role' IN ('admin', 'customer_service', 'engineer', 'viewer'));

-- RLS Policies for devices table
CREATE POLICY "Admin can do everything on devices" ON devices
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Customer service can manage devices" ON devices
    FOR ALL USING (auth.jwt() ->> 'role' IN ('admin', 'customer_service'));

CREATE POLICY "Engineers can view devices" ON devices
    FOR SELECT USING (auth.jwt() ->> 'role' IN ('admin', 'customer_service', 'engineer'));

-- RLS Policies for engineers table
CREATE POLICY "Admin can manage engineers" ON engineers
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Others can view active engineers" ON engineers
    FOR SELECT USING (
        auth.jwt() ->> 'role' IN ('admin', 'customer_service', 'engineer', 'viewer')
        AND is_active = true
    );

-- RLS Policies for service_requests table
CREATE POLICY "Admin can do everything on service_requests" ON service_requests
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Customer service can manage service_requests" ON service_requests
    FOR ALL USING (auth.jwt() ->> 'role' IN ('admin', 'customer_service'));

CREATE POLICY "Engineers can view and update assigned requests" ON service_requests
    FOR SELECT USING (
        auth.jwt() ->> 'role' IN ('admin', 'customer_service', 'engineer', 'viewer')
    );

CREATE POLICY "Engineers can update status of assigned requests" ON service_requests
    FOR UPDATE USING (
        auth.jwt() ->> 'role' = 'engineer'
        AND id IN (
            SELECT service_request_id FROM scheduling 
            WHERE engineer_id::text = auth.jwt() ->> 'user_id'
        )
    );

-- RLS Policies for scheduling table
CREATE POLICY "Admin can do everything on scheduling" ON scheduling
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Customer service can manage scheduling" ON scheduling
    FOR ALL USING (auth.jwt() ->> 'role' IN ('admin', 'customer_service'));

CREATE POLICY "Engineers can view their schedules" ON scheduling
    FOR SELECT USING (
        auth.jwt() ->> 'role' IN ('admin', 'customer_service', 'viewer')
        OR (auth.jwt() ->> 'role' = 'engineer' AND engineer_id::text = auth.jwt() ->> 'user_id')
    );

CREATE POLICY "Engineers can update their schedules" ON scheduling
    FOR UPDATE USING (
        auth.jwt() ->> 'role' = 'engineer'
        AND engineer_id::text = auth.jwt() ->> 'user_id'
    );

-- RLS Policies for parts table
CREATE POLICY "Admin can manage parts" ON parts
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Others can view parts" ON parts
    FOR SELECT USING (auth.jwt() ->> 'role' IN ('admin', 'customer_service', 'engineer', 'viewer'));

-- RLS Policies for execution table
CREATE POLICY "Admin can do everything on execution" ON execution
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Engineers can manage their executions" ON execution
    FOR ALL USING (
        auth.jwt() ->> 'role' = 'engineer'
        AND engineer_id::text = auth.jwt() ->> 'user_id'
    );

CREATE POLICY "Customer service can view executions" ON execution
    FOR SELECT USING (auth.jwt() ->> 'role' IN ('admin', 'customer_service', 'viewer'));

-- RLS Policies for execution_parts table
CREATE POLICY "Admin can do everything on execution_parts" ON execution_parts
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Engineers can manage parts in their executions" ON execution_parts
    FOR ALL USING (
        auth.jwt() ->> 'role' = 'engineer'
        AND execution_id IN (
            SELECT id FROM execution WHERE engineer_id::text = auth.jwt() ->> 'user_id'
        )
    );

CREATE POLICY "Others can view execution_parts" ON execution_parts
    FOR SELECT USING (auth.jwt() ->> 'role' IN ('admin', 'customer_service', 'viewer'));

-- RLS Policies for follow_up table
CREATE POLICY "Admin can do everything on follow_up" ON follow_up
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Customer service can manage follow_up" ON follow_up
    FOR ALL USING (auth.jwt() ->> 'role' IN ('admin', 'customer_service'));

CREATE POLICY "Others can view follow_up" ON follow_up
    FOR SELECT USING (auth.jwt() ->> 'role' IN ('admin', 'customer_service', 'engineer', 'viewer'));

-- RLS Policies for invoices table
CREATE POLICY "Admin can do everything on invoices" ON invoices
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Customer service can view invoices" ON invoices
    FOR SELECT USING (auth.jwt() ->> 'role' IN ('admin', 'customer_service'));

CREATE POLICY "Others can view basic invoice info" ON invoices
    FOR SELECT USING (
        auth.jwt() ->> 'role' IN ('engineer', 'viewer')
    );

-- Create views for easier data access
CREATE VIEW service_requests_with_details AS
SELECT 
    sr.*,
    c.full_name as customer_name,
    c.primary_phone as customer_phone,
    d.device_name,
    d.model as device_model,
    d.serial_number,
    e.full_name as engineer_name,
    s.scheduled_date,
    s.status as scheduling_status
FROM service_requests sr
LEFT JOIN customers c ON sr.customer_id = c.id
LEFT JOIN devices d ON sr.device_id = d.id
LEFT JOIN scheduling s ON sr.id = s.service_request_id
LEFT JOIN engineers e ON s.engineer_id = e.id;

-- Grant access to views
GRANT SELECT ON service_requests_with_details TO admin_role, customer_service_role, engineer_role, viewer_role;

-- Create function to get dashboard statistics
CREATE OR REPLACE FUNCTION get_dashboard_stats()
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'total_requests', (SELECT COUNT(*) FROM service_requests),
        'pending_requests', (SELECT COUNT(*) FROM service_requests WHERE status NOT IN ('مكتمل', 'مغلق')),
        'completed_requests', (SELECT COUNT(*) FROM service_requests WHERE status = 'مكتمل'),
        'active_engineers', (SELECT COUNT(*) FROM engineers WHERE is_active = true),
        'customer_satisfaction', (
            SELECT COALESCE(AVG(customer_rating), 0) 
            FROM follow_up 
            WHERE customer_rating IS NOT NULL
        ),
        'average_resolution_time', (
            SELECT COALESCE(AVG(EXTRACT(EPOCH FROM (closure_date - created_at))/3600), 0)
            FROM follow_up f
            JOIN service_requests sr ON f.service_request_id = sr.id
            WHERE f.closure_date IS NOT NULL
        )
    ) INTO result;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION get_dashboard_stats() TO admin_role, customer_service_role, viewer_role;
