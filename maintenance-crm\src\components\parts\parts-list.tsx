'use client'

import { useState } from 'react'
import { Part } from '@/types'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Search, 
  Plus,
  Edit,
  Package,
  AlertTriangle,
  DollarSign,
  TrendingDown,
  TrendingUp
} from 'lucide-react'

interface PartsListProps {
  parts: Part[]
  onEdit: (part: Part) => void
  onAdd: () => void
  onUpdateStock: (part: Part, newQuantity: number) => void
  isLoading?: boolean
}

export function PartsList({ 
  parts, 
  onEdit, 
  onAdd, 
  onUpdateStock,
  isLoading 
}: PartsListProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [stockFilter, setStockFilter] = useState<'all' | 'low' | 'out'>('all')

  // Filter parts based on search and stock filter
  const filteredParts = parts.filter(part => {
    const matchesSearch = !searchTerm.trim() || 
      part.part_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      part.part_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      part.description?.toLowerCase().includes(searchTerm.toLowerCase())

    let matchesStock = true
    switch (stockFilter) {
      case 'low':
        matchesStock = part.stock_quantity <= 10 && part.stock_quantity > 0
        break
      case 'out':
        matchesStock = part.stock_quantity === 0
        break
      default:
        matchesStock = true
    }

    return matchesSearch && matchesStock
  })

  const getStockStatus = (quantity: number) => {
    if (quantity === 0) {
      return { status: 'نفد المخزون', color: 'bg-red-100 text-red-800', icon: AlertTriangle }
    } else if (quantity <= 10) {
      return { status: 'مخزون منخفض', color: 'bg-yellow-100 text-yellow-800', icon: TrendingDown }
    } else {
      return { status: 'متوفر', color: 'bg-green-100 text-green-800', icon: TrendingUp }
    }
  }

  const handleStockUpdate = (part: Part, adjustment: number) => {
    const newQuantity = Math.max(0, part.stock_quantity + adjustment)
    onUpdateStock(part, newQuantity)
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="space-y-3">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header with Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex-1 max-w-md">
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="البحث في قطع الغيار..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pr-10"
            />
          </div>
        </div>
        <div className="flex gap-2">
          <Button
            variant={stockFilter === 'all' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setStockFilter('all')}
          >
            الكل
          </Button>
          <Button
            variant={stockFilter === 'low' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setStockFilter('low')}
          >
            مخزون منخفض
          </Button>
          <Button
            variant={stockFilter === 'out' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setStockFilter('out')}
          >
            نفد المخزون
          </Button>
          <Button onClick={onAdd} className="whitespace-nowrap">
            <Plus className="h-4 w-4 ml-2" />
            إضافة قطعة
          </Button>
        </div>
      </div>

      {/* Results Summary */}
      <div className="text-sm text-gray-600">
        {searchTerm || stockFilter !== 'all' ? (
          <span>
            تم العثور على {filteredParts.length} قطعة من أصل {parts.length}
          </span>
        ) : (
          <span>إجمالي قطع الغيار: {parts.length}</span>
        )}
      </div>

      {/* Parts Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredParts.length === 0 ? (
          <div className="col-span-full text-center py-12">
            <div className="text-gray-400 mb-4">
              <Package className="h-12 w-12 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchTerm || stockFilter !== 'all'
                ? 'لم يتم العثور على نتائج'
                : 'لا توجد قطع غيار'
              }
            </h3>
            <p className="text-gray-500 mb-4">
              {searchTerm || stockFilter !== 'all'
                ? 'جرب تغيير كلمات البحث أو المرشحات'
                : 'ابدأ بإضافة قطع غيار للمخزون'
              }
            </p>
            {(!searchTerm && stockFilter === 'all') && (
              <Button onClick={onAdd}>
                <Plus className="h-4 w-4 ml-2" />
                إضافة قطعة غيار
              </Button>
            )}
          </div>
        ) : (
          filteredParts.map((part) => {
            const stockStatus = getStockStatus(part.stock_quantity)
            const StatusIcon = stockStatus.icon

            return (
              <Card key={part.id} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-lg leading-tight mb-2">
                        {part.part_name}
                      </CardTitle>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">
                          {part.part_number}
                        </Badge>
                        <Badge className={stockStatus.color}>
                          <StatusIcon className="h-3 w-3 ml-1" />
                          {stockStatus.status}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Description */}
                  {part.description && (
                    <p className="text-sm text-gray-600 line-clamp-2">
                      {part.description}
                    </p>
                  )}

                  {/* Price and Stock */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <div className="flex items-center gap-2 text-sm">
                        <DollarSign className="h-4 w-4 text-gray-400" />
                        <span className="font-medium">{part.unit_price} ريال</span>
                      </div>
                      <p className="text-xs text-gray-500">سعر الوحدة</p>
                    </div>
                    <div>
                      <div className="flex items-center gap-2 text-sm">
                        <Package className="h-4 w-4 text-gray-400" />
                        <span className="font-medium">{part.stock_quantity}</span>
                      </div>
                      <p className="text-xs text-gray-500">الكمية المتوفرة</p>
                    </div>
                  </div>

                  {/* Total Value */}
                  <div className="bg-gray-50 rounded-lg p-3">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">القيمة الإجمالية:</span>
                      <span className="font-medium">
                        {(part.unit_price * part.stock_quantity).toFixed(2)} ريال
                      </span>
                    </div>
                  </div>

                  {/* Stock Adjustment */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">تعديل المخزون</label>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleStockUpdate(part, -1)}
                        disabled={part.stock_quantity === 0}
                      >
                        -1
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleStockUpdate(part, -5)}
                        disabled={part.stock_quantity < 5}
                      >
                        -5
                      </Button>
                      <span className="text-sm font-medium px-2">
                        {part.stock_quantity}
                      </span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleStockUpdate(part, 5)}
                      >
                        +5
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleStockUpdate(part, 1)}
                      >
                        +1
                      </Button>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex gap-2 pt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onEdit(part)}
                      className="flex-1"
                    >
                      <Edit className="h-4 w-4 ml-1" />
                      تعديل
                    </Button>
                  </div>

                  {/* Low Stock Warning */}
                  {part.stock_quantity <= 10 && part.stock_quantity > 0 && (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                      <div className="flex items-center gap-2 text-yellow-800">
                        <AlertTriangle className="h-4 w-4" />
                        <span className="text-sm font-medium">
                          المخزون منخفض - يُنصح بإعادة التموين
                        </span>
                      </div>
                    </div>
                  )}

                  {/* Out of Stock Warning */}
                  {part.stock_quantity === 0 && (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                      <div className="flex items-center gap-2 text-red-800">
                        <AlertTriangle className="h-4 w-4" />
                        <span className="text-sm font-medium">
                          نفد المخزون - يجب إعادة التموين فوراً
                        </span>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )
          })
        )}
      </div>
    </div>
  )
}
