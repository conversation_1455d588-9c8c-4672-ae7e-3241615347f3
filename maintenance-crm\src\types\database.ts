export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      customers: {
        Row: {
          id: string
          full_name: string
          primary_phone: string
          secondary_phone: string | null
          email: string | null
          detailed_address: string
          landmark: string | null
          contact_person: string | null
          special_notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          full_name: string
          primary_phone: string
          secondary_phone?: string | null
          email?: string | null
          detailed_address: string
          landmark?: string | null
          contact_person?: string | null
          special_notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          full_name?: string
          primary_phone?: string
          secondary_phone?: string | null
          email?: string | null
          detailed_address?: string
          landmark?: string | null
          contact_person?: string | null
          special_notes?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      devices: {
        Row: {
          id: string
          customer_id: string
          device_name: string
          model: string
          serial_number: string
          purchase_date: string | null
          warranty_end_date: string | null
          original_invoice_number: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          customer_id: string
          device_name: string
          model: string
          serial_number: string
          purchase_date?: string | null
          warranty_end_date?: string | null
          original_invoice_number?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          customer_id?: string
          device_name?: string
          model?: string
          serial_number?: string
          purchase_date?: string | null
          warranty_end_date?: string | null
          original_invoice_number?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      engineers: {
        Row: {
          id: string
          full_name: string
          phone: string
          email: string | null
          specialization: string | null
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          full_name: string
          phone: string
          email?: string | null
          specialization?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          full_name?: string
          phone?: string
          email?: string | null
          specialization?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      service_requests: {
        Row: {
          id: string
          customer_id: string
          device_id: string
          request_date: string
          received_by: string
          request_source: string
          initial_description: string
          priority: 'عاجل' | 'متوسط' | 'منخفض'
          status: 'قيد المراجعة' | 'تم تسجيله' | 'في انتظار فني' | 'تم تحديد موعد' | 'في الطريق للعميل' | 'قيد التنفيذ' | 'مكتمل' | 'مغلق'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          customer_id: string
          device_id: string
          request_date?: string
          received_by: string
          request_source: string
          initial_description: string
          priority: 'عاجل' | 'متوسط' | 'منخفض'
          status?: 'قيد المراجعة' | 'تم تسجيله' | 'في انتظار فني' | 'تم تحديد موعد' | 'في الطريق للعميل' | 'قيد التنفيذ' | 'مكتمل' | 'مغلق'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          customer_id?: string
          device_id?: string
          request_date?: string
          received_by?: string
          request_source?: string
          initial_description?: string
          priority?: 'عاجل' | 'متوسط' | 'منخفض'
          status?: 'قيد المراجعة' | 'تم تسجيله' | 'في انتظار فني' | 'تم تحديد موعد' | 'في الطريق للعميل' | 'قيد التنفيذ' | 'مكتمل' | 'مغلق'
          created_at?: string
          updated_at?: string
        }
      }
      scheduling: {
        Row: {
          id: string
          service_request_id: string
          engineer_id: string
          scheduled_date: string
          estimated_duration: number
          engineer_notes: string | null
          status: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          service_request_id: string
          engineer_id: string
          scheduled_date: string
          estimated_duration: number
          engineer_notes?: string | null
          status?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          service_request_id?: string
          engineer_id?: string
          scheduled_date?: string
          estimated_duration?: number
          engineer_notes?: string | null
          status?: string
          created_at?: string
          updated_at?: string
        }
      }
      execution: {
        Row: {
          id: string
          service_request_id: string
          engineer_id: string
          arrival_time: string | null
          start_time: string | null
          end_time: string | null
          technical_description: string
          repair_actions: string
          failure_cause: string | null
          tools_used: string | null
          future_recommendations: string | null
          received_by_customer: string | null
          device_status_after: string
          customer_signature: string | null
          technical_report: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          service_request_id: string
          engineer_id: string
          arrival_time?: string | null
          start_time?: string | null
          end_time?: string | null
          technical_description: string
          repair_actions: string
          failure_cause?: string | null
          tools_used?: string | null
          future_recommendations?: string | null
          received_by_customer?: string | null
          device_status_after: string
          customer_signature?: string | null
          technical_report?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          service_request_id?: string
          engineer_id?: string
          arrival_time?: string | null
          start_time?: string | null
          end_time?: string | null
          technical_description?: string
          repair_actions?: string
          failure_cause?: string | null
          tools_used?: string | null
          future_recommendations?: string | null
          received_by_customer?: string | null
          device_status_after?: string
          customer_signature?: string | null
          technical_report?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      parts: {
        Row: {
          id: string
          part_name: string
          part_number: string
          description: string | null
          unit_price: number
          stock_quantity: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          part_name: string
          part_number: string
          description?: string | null
          unit_price: number
          stock_quantity: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          part_name?: string
          part_number?: string
          description?: string | null
          unit_price?: number
          stock_quantity?: number
          created_at?: string
          updated_at?: string
        }
      }
      execution_parts: {
        Row: {
          id: string
          execution_id: string
          part_id: string
          quantity_used: number
          unit_price_at_time: number
          created_at: string
        }
        Insert: {
          id?: string
          execution_id: string
          part_id: string
          quantity_used: number
          unit_price_at_time: number
          created_at?: string
        }
        Update: {
          id?: string
          execution_id?: string
          part_id?: string
          quantity_used?: number
          unit_price_at_time?: number
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
