export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      customers: {
        Row: {
          id: string
          full_name: string
          primary_phone: string
          secondary_phone: string | null
          email: string | null
          detailed_address: string
          landmark: string | null
          contact_person: string | null
          special_notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          full_name: string
          primary_phone: string
          secondary_phone?: string | null
          email?: string | null
          detailed_address: string
          landmark?: string | null
          contact_person?: string | null
          special_notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          full_name?: string
          primary_phone?: string
          secondary_phone?: string | null
          email?: string | null
          detailed_address?: string
          landmark?: string | null
          contact_person?: string | null
          special_notes?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      devices: {
        Row: {
          id: string
          customer_id: string
          device_name: string
          model: string
          serial_number: string
          purchase_date: string | null
          warranty_end_date: string | null
          original_invoice_number: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          customer_id: string
          device_name: string
          model: string
          serial_number: string
          purchase_date?: string | null
          warranty_end_date?: string | null
          original_invoice_number?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          customer_id?: string
          device_name?: string
          model?: string
          serial_number?: string
          purchase_date?: string | null
          warranty_end_date?: string | null
          original_invoice_number?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      engineers: {
        Row: {
          id: string
          full_name: string
          phone: string
          email: string | null
          specialization: string | null
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          full_name: string
          phone: string
          email?: string | null
          specialization?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          full_name?: string
          phone?: string
          email?: string | null
          specialization?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      service_requests: {
        Row: {
          id: string
          customer_id: string
          device_id: string
          request_date: string
          received_by: string
          request_source: string
          initial_description: string
          priority: 'عاجل' | 'متوسط' | 'منخفض'
          status: 'قيد المراجعة' | 'تم تسجيله' | 'في انتظار فني' | 'تم تحديد موعد' | 'في الطريق للعميل' | 'قيد التنفيذ' | 'مكتمل' | 'مغلق'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          customer_id: string
          device_id: string
          request_date?: string
          received_by: string
          request_source: string
          initial_description: string
          priority: 'عاجل' | 'متوسط' | 'منخفض'
          status?: 'قيد المراجعة' | 'تم تسجيله' | 'في انتظار فني' | 'تم تحديد موعد' | 'في الطريق للعميل' | 'قيد التنفيذ' | 'مكتمل' | 'مغلق'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          customer_id?: string
          device_id?: string
          request_date?: string
          received_by?: string
          request_source?: string
          initial_description?: string
          priority?: 'عاجل' | 'متوسط' | 'منخفض'
          status?: 'قيد المراجعة' | 'تم تسجيله' | 'في انتظار فني' | 'تم تحديد موعد' | 'في الطريق للعميل' | 'قيد التنفيذ' | 'مكتمل' | 'مغلق'
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
