{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mY/AI/7-2025/CRM%20AGMENT/maintenance-crm/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: string | Date) {\n  const d = new Date(date)\n  return d.toLocaleDateString('ar-SA', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  })\n}\n\nexport function formatDateTime(date: string | Date) {\n  const d = new Date(date)\n  return d.toLocaleString('ar-SA', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit'\n  })\n}\n\nexport function formatTime(date: string | Date) {\n  const d = new Date(date)\n  return d.toLocaleTimeString('ar-SA', {\n    hour: '2-digit',\n    minute: '2-digit'\n  })\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,eAAe,IAAmB;IAChD,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,cAAc,CAAC,SAAS;QAC/B,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;AACF;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mY/AI/7-2025/CRM%20AGMENT/maintenance-crm/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 157, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mY/AI/7-2025/CRM%20AGMENT/maintenance-crm/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 224, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mY/AI/7-2025/CRM%20AGMENT/maintenance-crm/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 260, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mY/AI/7-2025/CRM%20AGMENT/maintenance-crm/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 308, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mY/AI/7-2025/CRM%20AGMENT/maintenance-crm/src/components/customers/customer-list.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Customer } from '@/types'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Badge } from '@/components/ui/badge'\nimport { \n  Search, \n  Phone, \n  Mail, \n  MapPin, \n  Edit, \n  Eye, \n  Plus,\n  User,\n  Building\n} from 'lucide-react'\nimport { formatDate } from '@/lib/utils'\n\ninterface CustomerListProps {\n  customers: Customer[]\n  onEdit: (customer: Customer) => void\n  onView: (customer: Customer) => void\n  onAdd: () => void\n  isLoading?: boolean\n}\n\nexport function CustomerList({ customers, onEdit, onView, onAdd, isLoading }: CustomerListProps) {\n  const [searchTerm, setSearchTerm] = useState('')\n  const [filteredCustomers, setFilteredCustomers] = useState(customers)\n\n  // Filter customers based on search term\n  const handleSearch = (term: string) => {\n    setSearchTerm(term)\n    if (!term.trim()) {\n      setFilteredCustomers(customers)\n      return\n    }\n\n    const filtered = customers.filter(customer =>\n      customer.full_name.toLowerCase().includes(term.toLowerCase()) ||\n      customer.primary_phone.includes(term) ||\n      customer.email?.toLowerCase().includes(term.toLowerCase()) ||\n      customer.detailed_address.toLowerCase().includes(term.toLowerCase())\n    )\n    setFilteredCustomers(filtered)\n  }\n\n  // Update filtered customers when customers prop changes\n  useState(() => {\n    if (!searchTerm.trim()) {\n      setFilteredCustomers(customers)\n    } else {\n      handleSearch(searchTerm)\n    }\n  }, [customers])\n\n  const isCompany = (customerName: string) => {\n    const companyKeywords = ['شركة', 'مؤسسة', 'مجموعة', 'شراكة', 'تجارة', 'خدمات']\n    return companyKeywords.some(keyword => customerName.includes(keyword))\n  }\n\n  if (isLoading) {\n    return (\n      <div className=\"space-y-4\">\n        {[...Array(3)].map((_, i) => (\n          <Card key={i} className=\"animate-pulse\">\n            <CardContent className=\"p-6\">\n              <div className=\"space-y-3\">\n                <div className=\"h-4 bg-gray-200 rounded w-1/4\"></div>\n                <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n                <div className=\"h-3 bg-gray-200 rounded w-1/3\"></div>\n              </div>\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header with Search and Add Button */}\n      <div className=\"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between\">\n        <div className=\"flex-1 max-w-md\">\n          <div className=\"relative\">\n            <Search className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n            <Input\n              placeholder=\"البحث عن عميل (الاسم، الهاتف، البريد الإلكتروني، العنوان)\"\n              value={searchTerm}\n              onChange={(e) => handleSearch(e.target.value)}\n              className=\"pr-10\"\n            />\n          </div>\n        </div>\n        <Button onClick={onAdd} className=\"whitespace-nowrap\">\n          <Plus className=\"h-4 w-4 ml-2\" />\n          إضافة عميل جديد\n        </Button>\n      </div>\n\n      {/* Results Summary */}\n      <div className=\"text-sm text-gray-600\">\n        {searchTerm ? (\n          <span>\n            تم العثور على {filteredCustomers.length} عميل من أصل {customers.length}\n          </span>\n        ) : (\n          <span>إجمالي العملاء: {customers.length}</span>\n        )}\n      </div>\n\n      {/* Customer Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        {filteredCustomers.length === 0 ? (\n          <div className=\"col-span-full text-center py-12\">\n            <div className=\"text-gray-400 mb-4\">\n              <User className=\"h-12 w-12 mx-auto\" />\n            </div>\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n              {searchTerm ? 'لم يتم العثور على نتائج' : 'لا توجد عملاء'}\n            </h3>\n            <p className=\"text-gray-500 mb-4\">\n              {searchTerm \n                ? 'جرب تغيير كلمات البحث أو إزالة المرشحات'\n                : 'ابدأ بإضافة عميل جديد للنظام'\n              }\n            </p>\n            {!searchTerm && (\n              <Button onClick={onAdd}>\n                <Plus className=\"h-4 w-4 ml-2\" />\n                إضافة عميل جديد\n              </Button>\n            )}\n          </div>\n        ) : (\n          filteredCustomers.map((customer) => (\n            <Card key={customer.id} className=\"hover:shadow-lg transition-shadow\">\n              <CardHeader className=\"pb-3\">\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex items-center gap-2\">\n                    {isCompany(customer.full_name) ? (\n                      <Building className=\"h-5 w-5 text-blue-600\" />\n                    ) : (\n                      <User className=\"h-5 w-5 text-green-600\" />\n                    )}\n                    <CardTitle className=\"text-lg leading-tight\">\n                      {customer.full_name}\n                    </CardTitle>\n                  </div>\n                  <Badge variant={isCompany(customer.full_name) ? \"default\" : \"secondary\"}>\n                    {isCompany(customer.full_name) ? 'مؤسسة' : 'فرد'}\n                  </Badge>\n                </div>\n                {customer.contact_person && (\n                  <p className=\"text-sm text-gray-600\">\n                    جهة الاتصال: {customer.contact_person}\n                  </p>\n                )}\n              </CardHeader>\n              <CardContent className=\"space-y-3\">\n                {/* Contact Information */}\n                <div className=\"space-y-2\">\n                  <div className=\"flex items-center gap-2 text-sm\">\n                    <Phone className=\"h-4 w-4 text-gray-400\" />\n                    <span className=\"font-medium\">{customer.primary_phone}</span>\n                  </div>\n                  {customer.secondary_phone && (\n                    <div className=\"flex items-center gap-2 text-sm text-gray-600\">\n                      <Phone className=\"h-4 w-4 text-gray-400\" />\n                      <span>{customer.secondary_phone}</span>\n                    </div>\n                  )}\n                  {customer.email && (\n                    <div className=\"flex items-center gap-2 text-sm text-gray-600\">\n                      <Mail className=\"h-4 w-4 text-gray-400\" />\n                      <span className=\"truncate\">{customer.email}</span>\n                    </div>\n                  )}\n                </div>\n\n                {/* Address */}\n                <div className=\"flex items-start gap-2 text-sm text-gray-600\">\n                  <MapPin className=\"h-4 w-4 text-gray-400 mt-0.5 flex-shrink-0\" />\n                  <span className=\"line-clamp-2\">{customer.detailed_address}</span>\n                </div>\n\n                {/* Landmark */}\n                {customer.landmark && (\n                  <div className=\"text-sm text-gray-500\">\n                    <span className=\"font-medium\">معلم بارز:</span> {customer.landmark}\n                  </div>\n                )}\n\n                {/* Special Notes */}\n                {customer.special_notes && (\n                  <div className=\"text-sm text-gray-500 bg-yellow-50 p-2 rounded\">\n                    <span className=\"font-medium\">ملاحظة:</span> {customer.special_notes}\n                  </div>\n                )}\n\n                {/* Creation Date */}\n                <div className=\"text-xs text-gray-400 pt-2 border-t\">\n                  تم الإنشاء: {formatDate(customer.created_at)}\n                </div>\n\n                {/* Action Buttons */}\n                <div className=\"flex gap-2 pt-3\">\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => onView(customer)}\n                    className=\"flex-1\"\n                  >\n                    <Eye className=\"h-4 w-4 ml-1\" />\n                    عرض\n                  </Button>\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => onEdit(customer)}\n                    className=\"flex-1\"\n                  >\n                    <Edit className=\"h-4 w-4 ml-1\" />\n                    تعديل\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          ))\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;;;AAnBA;;;;;;;;AA6BO,SAAS,aAAa,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAqB;;IAC7F,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,wCAAwC;IACxC,MAAM,eAAe,CAAC;QACpB,cAAc;QACd,IAAI,CAAC,KAAK,IAAI,IAAI;YAChB,qBAAqB;YACrB;QACF;QAEA,MAAM,WAAW,UAAU,MAAM,CAAC,CAAA,WAChC,SAAS,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,KAAK,WAAW,OAC1D,SAAS,aAAa,CAAC,QAAQ,CAAC,SAChC,SAAS,KAAK,EAAE,cAAc,SAAS,KAAK,WAAW,OACvD,SAAS,gBAAgB,CAAC,WAAW,GAAG,QAAQ,CAAC,KAAK,WAAW;QAEnE,qBAAqB;IACvB;IAEA,wDAAwD;IACxD,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;iCAAE;YACP,IAAI,CAAC,WAAW,IAAI,IAAI;gBACtB,qBAAqB;YACvB,OAAO;gBACL,aAAa;YACf;QACF;gCAAG;QAAC;KAAU;IAEd,MAAM,YAAY,CAAC;QACjB,MAAM,kBAAkB;YAAC;YAAQ;YAAS;YAAU;YAAS;YAAS;SAAQ;QAC9E,OAAO,gBAAgB,IAAI,CAAC,CAAA,UAAW,aAAa,QAAQ,CAAC;IAC/D;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACZ;mBAAI,MAAM;aAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,mIAAA,CAAA,OAAI;oBAAS,WAAU;8BACtB,cAAA,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;mBALV;;;;;;;;;;IAYnB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC,oIAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;oCAC5C,WAAU;;;;;;;;;;;;;;;;;kCAIhB,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAS;wBAAO,WAAU;;0CAChC,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMrC,6LAAC;gBAAI,WAAU;0BACZ,2BACC,6LAAC;;wBAAK;wBACW,kBAAkB,MAAM;wBAAC;wBAAc,UAAU,MAAM;;;;;;yCAGxE,6LAAC;;wBAAK;wBAAiB,UAAU,MAAM;;;;;;;;;;;;0BAK3C,6LAAC;gBAAI,WAAU;0BACZ,kBAAkB,MAAM,KAAK,kBAC5B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAElB,6LAAC;4BAAG,WAAU;sCACX,aAAa,4BAA4B;;;;;;sCAE5C,6LAAC;4BAAE,WAAU;sCACV,aACG,4CACA;;;;;;wBAGL,CAAC,4BACA,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAS;;8CACf,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;2BAMvC,kBAAkB,GAAG,CAAC,CAAC,yBACrB,6LAAC,mIAAA,CAAA,OAAI;wBAAmB,WAAU;;0CAChC,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;oDACZ,UAAU,SAAS,SAAS,kBAC3B,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;6EAEpB,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAElB,6LAAC,mIAAA,CAAA,YAAS;wDAAC,WAAU;kEAClB,SAAS,SAAS;;;;;;;;;;;;0DAGvB,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAS,UAAU,SAAS,SAAS,IAAI,YAAY;0DACzD,UAAU,SAAS,SAAS,IAAI,UAAU;;;;;;;;;;;;oCAG9C,SAAS,cAAc,kBACtB,6LAAC;wCAAE,WAAU;;4CAAwB;4CACrB,SAAS,cAAc;;;;;;;;;;;;;0CAI3C,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDAErB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;wDAAK,WAAU;kEAAe,SAAS,aAAa;;;;;;;;;;;;4CAEtD,SAAS,eAAe,kBACvB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;kEAAM,SAAS,eAAe;;;;;;;;;;;;4CAGlC,SAAS,KAAK,kBACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;wDAAK,WAAU;kEAAY,SAAS,KAAK;;;;;;;;;;;;;;;;;;kDAMhD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;gDAAK,WAAU;0DAAgB,SAAS,gBAAgB;;;;;;;;;;;;oCAI1D,SAAS,QAAQ,kBAChB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAc;;;;;;4CAAiB;4CAAE,SAAS,QAAQ;;;;;;;oCAKrE,SAAS,aAAa,kBACrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAc;;;;;;4CAAc;4CAAE,SAAS,aAAa;;;;;;;kDAKxE,6LAAC;wCAAI,WAAU;;4CAAsC;4CACtC,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,SAAS,UAAU;;;;;;;kDAI7C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,OAAO;gDACtB,WAAU;;kEAEV,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGlC,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,OAAO;gDACtB,WAAU;;kEAEV,6LAAC,8MAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;uBAtF9B,SAAS,EAAE;;;;;;;;;;;;;;;;AAiGlC;GA/MgB;KAAA", "debugId": null}}, {"offset": {"line": 889, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mY/AI/7-2025/CRM%20AGMENT/maintenance-crm/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG,oKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 927, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mY/AI/7-2025/CRM%20AGMENT/maintenance-crm/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 962, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mY/AI/7-2025/CRM%20AGMENT/maintenance-crm/src/components/customers/customer-form.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useForm } from 'react-hook-form'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport * as z from 'zod'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { CustomerFormData } from '@/types'\nimport { Save, X } from 'lucide-react'\n\nconst customerSchema = z.object({\n  full_name: z.string().min(2, 'الاسم الكامل مطلوب (حد أدنى حرفين)'),\n  primary_phone: z.string().min(10, 'رقم الهاتف الأساسي مطلوب').regex(/^[+]?[0-9\\s-()]+$/, 'رقم هاتف غير صحيح'),\n  secondary_phone: z.string().optional().refine((val) => !val || /^[+]?[0-9\\s-()]+$/.test(val), 'رقم هاتف غير صحيح'),\n  email: z.string().email('بريد إلكتروني غير صحيح').optional().or(z.literal('')),\n  detailed_address: z.string().min(10, 'العنوان التفصيلي مطلوب (حد أدنى 10 أحرف)'),\n  landmark: z.string().optional(),\n  contact_person: z.string().optional(),\n  special_notes: z.string().optional(),\n})\n\ninterface CustomerFormProps {\n  initialData?: Partial<CustomerFormData>\n  onSubmit: (data: CustomerFormData) => Promise<void>\n  onCancel: () => void\n  isLoading?: boolean\n}\n\nexport function CustomerForm({ initialData, onSubmit, onCancel, isLoading }: CustomerFormProps) {\n  const [isSubmitting, setIsSubmitting] = useState(false)\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n    reset\n  } = useForm<CustomerFormData>({\n    resolver: zodResolver(customerSchema),\n    defaultValues: initialData || {\n      full_name: '',\n      primary_phone: '',\n      secondary_phone: '',\n      email: '',\n      detailed_address: '',\n      landmark: '',\n      contact_person: '',\n      special_notes: '',\n    }\n  })\n\n  const handleFormSubmit = async (data: CustomerFormData) => {\n    setIsSubmitting(true)\n    try {\n      await onSubmit(data)\n      if (!initialData) {\n        reset() // Reset form only for new customers\n      }\n    } catch (error) {\n      console.error('Error submitting customer form:', error)\n    } finally {\n      setIsSubmitting(false)\n    }\n  }\n\n  return (\n    <Card className=\"w-full max-w-2xl mx-auto\">\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          {initialData ? 'تعديل بيانات العميل' : 'إضافة عميل جديد'}\n        </CardTitle>\n        <CardDescription>\n          {initialData \n            ? 'قم بتعديل بيانات العميل أدناه' \n            : 'أدخل بيانات العميل الجديد أدناه'\n          }\n        </CardDescription>\n      </CardHeader>\n      <CardContent>\n        <form onSubmit={handleSubmit(handleFormSubmit)} className=\"space-y-6\">\n          {/* Basic Information */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">المعلومات الأساسية</h3>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"full_name\">الاسم الكامل *</Label>\n                <Input\n                  id=\"full_name\"\n                  {...register('full_name')}\n                  placeholder=\"أدخل الاسم الكامل\"\n                  className={errors.full_name ? 'border-red-500' : ''}\n                />\n                {errors.full_name && (\n                  <p className=\"text-sm text-red-500\">{errors.full_name.message}</p>\n                )}\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"contact_person\">جهة الاتصال (للمؤسسات)</Label>\n                <Input\n                  id=\"contact_person\"\n                  {...register('contact_person')}\n                  placeholder=\"اسم الشخص المسؤول\"\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Contact Information */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">معلومات الاتصال</h3>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"primary_phone\">رقم الهاتف الأساسي *</Label>\n                <Input\n                  id=\"primary_phone\"\n                  {...register('primary_phone')}\n                  placeholder=\"+966 5X XXX XXXX\"\n                  className={errors.primary_phone ? 'border-red-500' : ''}\n                />\n                {errors.primary_phone && (\n                  <p className=\"text-sm text-red-500\">{errors.primary_phone.message}</p>\n                )}\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"secondary_phone\">رقم الهاتف الاحتياطي</Label>\n                <Input\n                  id=\"secondary_phone\"\n                  {...register('secondary_phone')}\n                  placeholder=\"+966 5X XXX XXXX\"\n                  className={errors.secondary_phone ? 'border-red-500' : ''}\n                />\n                {errors.secondary_phone && (\n                  <p className=\"text-sm text-red-500\">{errors.secondary_phone.message}</p>\n                )}\n              </div>\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"email\">البريد الإلكتروني</Label>\n              <Input\n                id=\"email\"\n                type=\"email\"\n                {...register('email')}\n                placeholder=\"<EMAIL>\"\n                className={errors.email ? 'border-red-500' : ''}\n              />\n              {errors.email && (\n                <p className=\"text-sm text-red-500\">{errors.email.message}</p>\n              )}\n            </div>\n          </div>\n\n          {/* Address Information */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">معلومات العنوان</h3>\n            \n            <div className=\"space-y-2\">\n              <Label htmlFor=\"detailed_address\">العنوان التفصيلي *</Label>\n              <Textarea\n                id=\"detailed_address\"\n                {...register('detailed_address')}\n                placeholder=\"أدخل العنوان التفصيلي مع اسم الحي والشارع\"\n                className={errors.detailed_address ? 'border-red-500' : ''}\n                rows={3}\n              />\n              {errors.detailed_address && (\n                <p className=\"text-sm text-red-500\">{errors.detailed_address.message}</p>\n              )}\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"landmark\">معلم بارز</Label>\n              <Input\n                id=\"landmark\"\n                {...register('landmark')}\n                placeholder=\"أقرب معلم بارز للعنوان\"\n              />\n            </div>\n          </div>\n\n          {/* Additional Notes */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">ملاحظات إضافية</h3>\n            \n            <div className=\"space-y-2\">\n              <Label htmlFor=\"special_notes\">ملاحظات خاصة</Label>\n              <Textarea\n                id=\"special_notes\"\n                {...register('special_notes')}\n                placeholder=\"أي ملاحظات خاصة (أفضل وقت للاتصال، حساسية معينة، إلخ)\"\n                rows={3}\n              />\n            </div>\n          </div>\n\n          {/* Form Actions */}\n          <div className=\"flex gap-4 pt-6\">\n            <Button\n              type=\"submit\"\n              disabled={isSubmitting || isLoading}\n              className=\"flex-1\"\n            >\n              <Save className=\"h-4 w-4 ml-2\" />\n              {isSubmitting ? 'جاري الحفظ...' : 'حفظ البيانات'}\n            </Button>\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={onCancel}\n              disabled={isSubmitting || isLoading}\n            >\n              <X className=\"h-4 w-4 ml-2\" />\n              إلغاء\n            </Button>\n          </div>\n        </form>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;;;AAZA;;;;;;;;;;;AAcA,MAAM,iBAAiB,CAAA,GAAA,kJAAA,CAAA,SAAQ,AAAD,EAAE;IAC9B,WAAW,CAAA,GAAA,kJAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;IAC7B,eAAe,CAAA,GAAA,kJAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,IAAI,4BAA4B,KAAK,CAAC,qBAAqB;IACzF,iBAAiB,CAAA,GAAA,kJAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ,GAAG,MAAM,CAAC,CAAC,MAAQ,CAAC,OAAO,oBAAoB,IAAI,CAAC,MAAM;IAC9F,OAAO,CAAA,GAAA,kJAAA,CAAA,SAAQ,AAAD,IAAI,KAAK,CAAC,0BAA0B,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,kJAAA,CAAA,UAAS,AAAD,EAAE;IAC1E,kBAAkB,CAAA,GAAA,kJAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,IAAI;IACrC,UAAU,CAAA,GAAA,kJAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;IAC7B,gBAAgB,CAAA,GAAA,kJAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;IACnC,eAAe,CAAA,GAAA,kJAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;AACpC;AASO,SAAS,aAAa,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAqB;;IAC5F,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACrB,KAAK,EACN,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAoB;QAC5B,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe,eAAe;YAC5B,WAAW;YACX,eAAe;YACf,iBAAiB;YACjB,OAAO;YACP,kBAAkB;YAClB,UAAU;YACV,gBAAgB;YAChB,eAAe;QACjB;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,gBAAgB;QAChB,IAAI;YACF,MAAM,SAAS;YACf,IAAI,CAAC,aAAa;gBAChB,QAAQ,oCAAoC;;YAC9C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;;kCACT,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;kCAClB,cAAc,wBAAwB;;;;;;kCAEzC,6LAAC,mIAAA,CAAA,kBAAe;kCACb,cACG,kCACA;;;;;;;;;;;;0BAIR,6LAAC,mIAAA,CAAA,cAAW;0BACV,cAAA,6LAAC;oBAAK,UAAU,aAAa;oBAAmB,WAAU;;sCAExD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAEpD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAY;;;;;;8DAC3B,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACF,GAAG,SAAS,YAAY;oDACzB,aAAY;oDACZ,WAAW,OAAO,SAAS,GAAG,mBAAmB;;;;;;gDAElD,OAAO,SAAS,kBACf,6LAAC;oDAAE,WAAU;8DAAwB,OAAO,SAAS,CAAC,OAAO;;;;;;;;;;;;sDAIjE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAiB;;;;;;8DAChC,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACF,GAAG,SAAS,iBAAiB;oDAC9B,aAAY;;;;;;;;;;;;;;;;;;;;;;;;sCAOpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAEpD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAgB;;;;;;8DAC/B,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACF,GAAG,SAAS,gBAAgB;oDAC7B,aAAY;oDACZ,WAAW,OAAO,aAAa,GAAG,mBAAmB;;;;;;gDAEtD,OAAO,aAAa,kBACnB,6LAAC;oDAAE,WAAU;8DAAwB,OAAO,aAAa,CAAC,OAAO;;;;;;;;;;;;sDAIrE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAkB;;;;;;8DACjC,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACF,GAAG,SAAS,kBAAkB;oDAC/B,aAAY;oDACZ,WAAW,OAAO,eAAe,GAAG,mBAAmB;;;;;;gDAExD,OAAO,eAAe,kBACrB,6LAAC;oDAAE,WAAU;8DAAwB,OAAO,eAAe,CAAC,OAAO;;;;;;;;;;;;;;;;;;8CAKzE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAQ;;;;;;sDACvB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACJ,GAAG,SAAS,QAAQ;4CACrB,aAAY;4CACZ,WAAW,OAAO,KAAK,GAAG,mBAAmB;;;;;;wCAE9C,OAAO,KAAK,kBACX,6LAAC;4CAAE,WAAU;sDAAwB,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;;;;;;;sCAM/D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAEpD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAmB;;;;;;sDAClC,6LAAC,uIAAA,CAAA,WAAQ;4CACP,IAAG;4CACF,GAAG,SAAS,mBAAmB;4CAChC,aAAY;4CACZ,WAAW,OAAO,gBAAgB,GAAG,mBAAmB;4CACxD,MAAM;;;;;;wCAEP,OAAO,gBAAgB,kBACtB,6LAAC;4CAAE,WAAU;sDAAwB,OAAO,gBAAgB,CAAC,OAAO;;;;;;;;;;;;8CAIxE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAW;;;;;;sDAC1B,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACF,GAAG,SAAS,WAAW;4CACxB,aAAY;;;;;;;;;;;;;;;;;;sCAMlB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAEpD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAgB;;;;;;sDAC/B,6LAAC,uIAAA,CAAA,WAAQ;4CACP,IAAG;4CACF,GAAG,SAAS,gBAAgB;4CAC7B,aAAY;4CACZ,MAAM;;;;;;;;;;;;;;;;;;sCAMZ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,UAAU,gBAAgB;oCAC1B,WAAU;;sDAEV,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCACf,eAAe,kBAAkB;;;;;;;8CAEpC,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU,gBAAgB;;sDAE1B,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5C;GAlMgB;;QAQV,iKAAA,CAAA,UAAO;;;KARG", "debugId": null}}, {"offset": {"line": 1486, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mY/AI/7-2025/CRM%20AGMENT/maintenance-crm/src/components/customers/customer-details.tsx"], "sourcesContent": ["'use client'\n\nimport { <PERSON>er, <PERSON>ce, ServiceRequest } from '@/types'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { \n  Phone, \n  Mail, \n  MapPin, \n  Edit, \n  Plus,\n  User,\n  Building,\n  Calendar,\n  Wrench,\n  Monitor,\n  Clock\n} from 'lucide-react'\nimport { formatDate, formatDateTime } from '@/lib/utils'\n\ninterface CustomerDetailsProps {\n  customer: Customer\n  devices?: Device[]\n  recentRequests?: ServiceRequest[]\n  onEdit: () => void\n  onAddDevice: () => void\n  onAddServiceRequest: () => void\n  isLoading?: boolean\n}\n\nexport function CustomerDetails({ \n  customer, \n  devices = [], \n  recentRequests = [],\n  onEdit, \n  onAddDevice, \n  onAddServiceRequest,\n  isLoading \n}: CustomerDetailsProps) {\n  const isCompany = (customerName: string) => {\n    const companyKeywords = ['شركة', 'مؤسسة', 'مجموعة', 'شراكة', 'تجارة', 'خدمات']\n    return companyKeywords.some(keyword => customerName.includes(keyword))\n  }\n\n  const getStatusColor = (status: string) => {\n    const statusColors: Record<string, string> = {\n      'قيد المراجعة': 'bg-yellow-100 text-yellow-800',\n      'تم تسجيله': 'bg-blue-100 text-blue-800',\n      'في انتظار فني': 'bg-orange-100 text-orange-800',\n      'تم تحديد موعد': 'bg-purple-100 text-purple-800',\n      'في الطريق للعميل': 'bg-indigo-100 text-indigo-800',\n      'قيد التنفيذ': 'bg-green-100 text-green-800',\n      'مكتمل': 'bg-green-100 text-green-800',\n      'مغلق': 'bg-gray-100 text-gray-800',\n    }\n    return statusColors[status] || 'bg-gray-100 text-gray-800'\n  }\n\n  const getPriorityColor = (priority: string) => {\n    const priorityColors: Record<string, string> = {\n      'عاجل': 'bg-red-100 text-red-800',\n      'متوسط': 'bg-yellow-100 text-yellow-800',\n      'منخفض': 'bg-green-100 text-green-800',\n    }\n    return priorityColors[priority] || 'bg-gray-100 text-gray-800'\n  }\n\n  if (isLoading) {\n    return (\n      <div className=\"space-y-6\">\n        <Card className=\"animate-pulse\">\n          <CardContent className=\"p-6\">\n            <div className=\"space-y-4\">\n              <div className=\"h-6 bg-gray-200 rounded w-1/3\"></div>\n              <div className=\"h-4 bg-gray-200 rounded w-1/2\"></div>\n              <div className=\"h-4 bg-gray-200 rounded w-2/3\"></div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Customer Information Card */}\n      <Card>\n        <CardHeader>\n          <div className=\"flex items-start justify-between\">\n            <div className=\"flex items-center gap-3\">\n              {isCompany(customer.full_name) ? (\n                <Building className=\"h-8 w-8 text-blue-600\" />\n              ) : (\n                <User className=\"h-8 w-8 text-green-600\" />\n              )}\n              <div>\n                <CardTitle className=\"text-2xl\">{customer.full_name}</CardTitle>\n                <div className=\"flex items-center gap-2 mt-1\">\n                  <Badge variant={isCompany(customer.full_name) ? \"default\" : \"secondary\"}>\n                    {isCompany(customer.full_name) ? 'مؤسسة' : 'فرد'}\n                  </Badge>\n                  {customer.contact_person && (\n                    <span className=\"text-sm text-gray-600\">\n                      جهة الاتصال: {customer.contact_person}\n                    </span>\n                  )}\n                </div>\n              </div>\n            </div>\n            <Button onClick={onEdit} variant=\"outline\">\n              <Edit className=\"h-4 w-4 ml-2\" />\n              تعديل البيانات\n            </Button>\n          </div>\n        </CardHeader>\n        <CardContent className=\"space-y-6\">\n          {/* Contact Information */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-3\">معلومات الاتصال</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"flex items-center gap-3\">\n                <Phone className=\"h-5 w-5 text-gray-400\" />\n                <div>\n                  <p className=\"font-medium\">{customer.primary_phone}</p>\n                  <p className=\"text-sm text-gray-500\">الهاتف الأساسي</p>\n                </div>\n              </div>\n              {customer.secondary_phone && (\n                <div className=\"flex items-center gap-3\">\n                  <Phone className=\"h-5 w-5 text-gray-400\" />\n                  <div>\n                    <p className=\"font-medium\">{customer.secondary_phone}</p>\n                    <p className=\"text-sm text-gray-500\">الهاتف الاحتياطي</p>\n                  </div>\n                </div>\n              )}\n              {customer.email && (\n                <div className=\"flex items-center gap-3 md:col-span-2\">\n                  <Mail className=\"h-5 w-5 text-gray-400\" />\n                  <div>\n                    <p className=\"font-medium\">{customer.email}</p>\n                    <p className=\"text-sm text-gray-500\">البريد الإلكتروني</p>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Address Information */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-3\">معلومات العنوان</h3>\n            <div className=\"flex items-start gap-3\">\n              <MapPin className=\"h-5 w-5 text-gray-400 mt-1\" />\n              <div className=\"flex-1\">\n                <p className=\"font-medium\">{customer.detailed_address}</p>\n                {customer.landmark && (\n                  <p className=\"text-sm text-gray-500 mt-1\">\n                    <span className=\"font-medium\">معلم بارز:</span> {customer.landmark}\n                  </p>\n                )}\n              </div>\n            </div>\n          </div>\n\n          {/* Special Notes */}\n          {customer.special_notes && (\n            <div>\n              <h3 className=\"text-lg font-semibold mb-3\">ملاحظات خاصة</h3>\n              <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n                <p className=\"text-gray-700\">{customer.special_notes}</p>\n              </div>\n            </div>\n          )}\n\n          {/* Metadata */}\n          <div className=\"border-t pt-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-500\">\n              <div className=\"flex items-center gap-2\">\n                <Calendar className=\"h-4 w-4\" />\n                <span>تاريخ الإنشاء: {formatDate(customer.created_at)}</span>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <Clock className=\"h-4 w-4\" />\n                <span>آخر تحديث: {formatDate(customer.updated_at)}</span>\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Devices Section */}\n      <Card>\n        <CardHeader>\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <CardTitle className=\"flex items-center gap-2\">\n                <Monitor className=\"h-5 w-5\" />\n                الأجهزة ({devices.length})\n              </CardTitle>\n              <CardDescription>\n                قائمة الأجهزة المسجلة لهذا العميل\n              </CardDescription>\n            </div>\n            <Button onClick={onAddDevice} size=\"sm\">\n              <Plus className=\"h-4 w-4 ml-2\" />\n              إضافة جهاز\n            </Button>\n          </div>\n        </CardHeader>\n        <CardContent>\n          {devices.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <Monitor className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n              <p className=\"text-gray-500 mb-4\">لا توجد أجهزة مسجلة لهذا العميل</p>\n              <Button onClick={onAddDevice} variant=\"outline\">\n                <Plus className=\"h-4 w-4 ml-2\" />\n                إضافة جهاز جديد\n              </Button>\n            </div>\n          ) : (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              {devices.map((device) => (\n                <div key={device.id} className=\"border rounded-lg p-4\">\n                  <div className=\"flex items-start justify-between mb-2\">\n                    <h4 className=\"font-semibold\">{device.device_name}</h4>\n                    <Badge variant=\"outline\">{device.model}</Badge>\n                  </div>\n                  <p className=\"text-sm text-gray-600 mb-2\">\n                    الرقم التسلسلي: {device.serial_number}\n                  </p>\n                  {device.warranty_end_date && (\n                    <p className=\"text-sm text-gray-500\">\n                      انتهاء الضمان: {formatDate(device.warranty_end_date)}\n                    </p>\n                  )}\n                </div>\n              ))}\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Recent Service Requests */}\n      <Card>\n        <CardHeader>\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <CardTitle className=\"flex items-center gap-2\">\n                <Wrench className=\"h-5 w-5\" />\n                طلبات الصيانة الأخيرة ({recentRequests.length})\n              </CardTitle>\n              <CardDescription>\n                آخر طلبات الصيانة لهذا العميل\n              </CardDescription>\n            </div>\n            <Button onClick={onAddServiceRequest} size=\"sm\">\n              <Plus className=\"h-4 w-4 ml-2\" />\n              طلب صيانة جديد\n            </Button>\n          </div>\n        </CardHeader>\n        <CardContent>\n          {recentRequests.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <Wrench className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n              <p className=\"text-gray-500 mb-4\">لا توجد طلبات صيانة لهذا العميل</p>\n              <Button onClick={onAddServiceRequest} variant=\"outline\">\n                <Plus className=\"h-4 w-4 ml-2\" />\n                إنشاء طلب صيانة جديد\n              </Button>\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              {recentRequests.slice(0, 5).map((request) => (\n                <div key={request.id} className=\"border rounded-lg p-4\">\n                  <div className=\"flex items-start justify-between mb-2\">\n                    <div className=\"flex-1\">\n                      <p className=\"font-medium line-clamp-2\">{request.initial_description}</p>\n                      <p className=\"text-sm text-gray-500 mt-1\">\n                        {formatDateTime(request.request_date)}\n                      </p>\n                    </div>\n                    <div className=\"flex gap-2 ml-4\">\n                      <Badge className={getPriorityColor(request.priority)}>\n                        {request.priority}\n                      </Badge>\n                      <Badge className={getStatusColor(request.status)}>\n                        {request.status}\n                      </Badge>\n                    </div>\n                  </div>\n                </div>\n              ))}\n              {recentRequests.length > 5 && (\n                <div className=\"text-center pt-4\">\n                  <Button variant=\"outline\" size=\"sm\">\n                    عرض جميع الطلبات ({recentRequests.length})\n                  </Button>\n                </div>\n              )}\n            </div>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AAnBA;;;;;;;AA+BO,SAAS,gBAAgB,EAC9B,QAAQ,EACR,UAAU,EAAE,EACZ,iBAAiB,EAAE,EACnB,MAAM,EACN,WAAW,EACX,mBAAmB,EACnB,SAAS,EACY;IACrB,MAAM,YAAY,CAAC;QACjB,MAAM,kBAAkB;YAAC;YAAQ;YAAS;YAAU;YAAS;YAAS;SAAQ;QAC9E,OAAO,gBAAgB,IAAI,CAAC,CAAA,UAAW,aAAa,QAAQ,CAAC;IAC/D;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,eAAuC;YAC3C,gBAAgB;YAChB,aAAa;YACb,iBAAiB;YACjB,iBAAiB;YACjB,oBAAoB;YACpB,eAAe;YACf,SAAS;YACT,QAAQ;QACV;QACA,OAAO,YAAY,CAAC,OAAO,IAAI;IACjC;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,iBAAyC;YAC7C,QAAQ;YACR,SAAS;YACT,SAAS;QACX;QACA,OAAO,cAAc,CAAC,SAAS,IAAI;IACrC;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;IAM3B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;wCACZ,UAAU,SAAS,SAAS,kBAC3B,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;iEAEpB,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAElB,6LAAC;;8DACC,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAY,SAAS,SAAS;;;;;;8DACnD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAS,UAAU,SAAS,SAAS,IAAI,YAAY;sEACzD,UAAU,SAAS,SAAS,IAAI,UAAU;;;;;;wDAE5C,SAAS,cAAc,kBACtB,6LAAC;4DAAK,WAAU;;gEAAwB;gEACxB,SAAS,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;8CAM/C,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAQ,SAAQ;;sDAC/B,6LAAC,8MAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;kCAKvC,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CAErB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAe,SAAS,aAAa;;;;;;0EAClD,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;4CAGxC,SAAS,eAAe,kBACvB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAe,SAAS,eAAe;;;;;;0EACpD,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;4CAI1C,SAAS,KAAK,kBACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAe,SAAS,KAAK;;;;;;0EAC1C,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQ/C,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAe,SAAS,gBAAgB;;;;;;oDACpD,SAAS,QAAQ,kBAChB,6LAAC;wDAAE,WAAU;;0EACX,6LAAC;gEAAK,WAAU;0EAAc;;;;;;4DAAiB;4DAAE,SAAS,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;4BAQ3E,SAAS,aAAa,kBACrB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAAiB,SAAS,aAAa;;;;;;;;;;;;;;;;;0CAM1D,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;;wDAAK;wDAAgB,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,SAAS,UAAU;;;;;;;;;;;;;sDAEtD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;;wDAAK;wDAAY,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,SAAS,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ1D,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,2MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAAY;gDACrB,QAAQ,MAAM;gDAAC;;;;;;;sDAE3B,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAa,MAAK;;sDACjC,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;kCAKvC,6LAAC,mIAAA,CAAA,cAAW;kCACT,QAAQ,MAAM,KAAK,kBAClB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,2MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAa,SAAQ;;sDACpC,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;iDAKrC,6LAAC;4BAAI,WAAU;sCACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;oCAAoB,WAAU;;sDAC7B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAiB,OAAO,WAAW;;;;;;8DACjD,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAW,OAAO,KAAK;;;;;;;;;;;;sDAExC,6LAAC;4CAAE,WAAU;;gDAA6B;gDACvB,OAAO,aAAa;;;;;;;wCAEtC,OAAO,iBAAiB,kBACvB,6LAAC;4CAAE,WAAU;;gDAAwB;gDACnB,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,OAAO,iBAAiB;;;;;;;;mCAV/C,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;0BAqB7B,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAY;gDACN,eAAe,MAAM;gDAAC;;;;;;;sDAEhD,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAqB,MAAK;;sDACzC,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;kCAKvC,6LAAC,mIAAA,CAAA,cAAW;kCACT,eAAe,MAAM,KAAK,kBACzB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAqB,SAAQ;;sDAC5C,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;iDAKrC,6LAAC;4BAAI,WAAU;;gCACZ,eAAe,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,wBAC/B,6LAAC;wCAAqB,WAAU;kDAC9B,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAA4B,QAAQ,mBAAmB;;;;;;sEACpE,6LAAC;4DAAE,WAAU;sEACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,YAAY;;;;;;;;;;;;8DAGxC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;4DAAC,WAAW,iBAAiB,QAAQ,QAAQ;sEAChD,QAAQ,QAAQ;;;;;;sEAEnB,6LAAC,oIAAA,CAAA,QAAK;4DAAC,WAAW,eAAe,QAAQ,MAAM;sEAC5C,QAAQ,MAAM;;;;;;;;;;;;;;;;;;uCAbb,QAAQ,EAAE;;;;;gCAmBrB,eAAe,MAAM,GAAG,mBACvB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;;4CAAK;4CACf,eAAe,MAAM;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU7D;KApRgB", "debugId": null}}, {"offset": {"line": 2450, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mY/AI/7-2025/CRM%20AGMENT/maintenance-crm/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { Database } from '@/types/database'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://demo.supabase.co'\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'demo-key'\n\nexport const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey)\n"], "names": [], "mappings": ";;;AAGoB;AAHpB;;AAGA,MAAM,cAAc,wEAAwC;AAC5D,MAAM,kBAAkB,qFAA6C;AAE9D,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAY,aAAa", "debugId": null}}, {"offset": {"line": 2468, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mY/AI/7-2025/CRM%20AGMENT/maintenance-crm/src/lib/api/customers.ts"], "sourcesContent": ["import { supabase } from '@/lib/supabase'\nimport { Customer, CustomerFormData, ApiResponse } from '@/types'\n\nexport class CustomersAPI {\n  // Get all customers\n  static async getAll(): Promise<ApiResponse<Customer[]>> {\n    try {\n      const { data, error } = await supabase\n        .from('customers')\n        .select('*')\n        .order('created_at', { ascending: false })\n\n      if (error) {\n        console.error('Error fetching customers:', error)\n        return { error: 'فشل في جلب بيانات العملاء' }\n      }\n\n      return { data: data || [] }\n    } catch (error) {\n      console.error('Unexpected error:', error)\n      return { error: 'حدث خطأ غير متوقع' }\n    }\n  }\n\n  // Get customer by ID\n  static async getById(id: string): Promise<ApiResponse<Customer>> {\n    try {\n      const { data, error } = await supabase\n        .from('customers')\n        .select('*')\n        .eq('id', id)\n        .single()\n\n      if (error) {\n        console.error('Error fetching customer:', error)\n        return { error: 'فشل في جلب بيانات العميل' }\n      }\n\n      if (!data) {\n        return { error: 'العميل غير موجود' }\n      }\n\n      return { data }\n    } catch (error) {\n      console.error('Unexpected error:', error)\n      return { error: 'حدث خطأ غير متوقع' }\n    }\n  }\n\n  // Create new customer\n  static async create(customerData: CustomerFormData): Promise<ApiResponse<Customer>> {\n    try {\n      // Check if phone number already exists\n      const { data: existingCustomer } = await supabase\n        .from('customers')\n        .select('id')\n        .eq('primary_phone', customerData.primary_phone)\n        .single()\n\n      if (existingCustomer) {\n        return { error: 'رقم الهاتف مسجل مسبقاً لعميل آخر' }\n      }\n\n      const { data, error } = await supabase\n        .from('customers')\n        .insert([customerData])\n        .select()\n        .single()\n\n      if (error) {\n        console.error('Error creating customer:', error)\n        return { error: 'فشل في إنشاء العميل' }\n      }\n\n      return { \n        data, \n        message: 'تم إنشاء العميل بنجاح' \n      }\n    } catch (error) {\n      console.error('Unexpected error:', error)\n      return { error: 'حدث خطأ غير متوقع' }\n    }\n  }\n\n  // Update customer\n  static async update(id: string, customerData: Partial<CustomerFormData>): Promise<ApiResponse<Customer>> {\n    try {\n      // If updating phone number, check if it's already used by another customer\n      if (customerData.primary_phone) {\n        const { data: existingCustomer } = await supabase\n          .from('customers')\n          .select('id')\n          .eq('primary_phone', customerData.primary_phone)\n          .neq('id', id)\n          .single()\n\n        if (existingCustomer) {\n          return { error: 'رقم الهاتف مسجل مسبقاً لعميل آخر' }\n        }\n      }\n\n      const { data, error } = await supabase\n        .from('customers')\n        .update(customerData)\n        .eq('id', id)\n        .select()\n        .single()\n\n      if (error) {\n        console.error('Error updating customer:', error)\n        return { error: 'فشل في تحديث بيانات العميل' }\n      }\n\n      return { \n        data, \n        message: 'تم تحديث بيانات العميل بنجاح' \n      }\n    } catch (error) {\n      console.error('Unexpected error:', error)\n      return { error: 'حدث خطأ غير متوقع' }\n    }\n  }\n\n  // Delete customer\n  static async delete(id: string): Promise<ApiResponse<void>> {\n    try {\n      // Check if customer has any devices or service requests\n      const { data: devices } = await supabase\n        .from('devices')\n        .select('id')\n        .eq('customer_id', id)\n        .limit(1)\n\n      if (devices && devices.length > 0) {\n        return { error: 'لا يمكن حذف العميل لأنه يحتوي على أجهزة مسجلة' }\n      }\n\n      const { data: serviceRequests } = await supabase\n        .from('service_requests')\n        .select('id')\n        .eq('customer_id', id)\n        .limit(1)\n\n      if (serviceRequests && serviceRequests.length > 0) {\n        return { error: 'لا يمكن حذف العميل لأنه يحتوي على طلبات صيانة' }\n      }\n\n      const { error } = await supabase\n        .from('customers')\n        .delete()\n        .eq('id', id)\n\n      if (error) {\n        console.error('Error deleting customer:', error)\n        return { error: 'فشل في حذف العميل' }\n      }\n\n      return { message: 'تم حذف العميل بنجاح' }\n    } catch (error) {\n      console.error('Unexpected error:', error)\n      return { error: 'حدث خطأ غير متوقع' }\n    }\n  }\n\n  // Search customers\n  static async search(query: string): Promise<ApiResponse<Customer[]>> {\n    try {\n      const { data, error } = await supabase\n        .from('customers')\n        .select('*')\n        .or(`full_name.ilike.%${query}%,primary_phone.like.%${query}%,email.ilike.%${query}%,detailed_address.ilike.%${query}%`)\n        .order('created_at', { ascending: false })\n\n      if (error) {\n        console.error('Error searching customers:', error)\n        return { error: 'فشل في البحث عن العملاء' }\n      }\n\n      return { data: data || [] }\n    } catch (error) {\n      console.error('Unexpected error:', error)\n      return { error: 'حدث خطأ غير متوقع' }\n    }\n  }\n\n  // Get customer statistics\n  static async getStatistics(): Promise<ApiResponse<{\n    total: number\n    companies: number\n    individuals: number\n    recentlyAdded: number\n  }>> {\n    try {\n      const { data: allCustomers, error } = await supabase\n        .from('customers')\n        .select('full_name, created_at')\n\n      if (error) {\n        console.error('Error fetching customer statistics:', error)\n        return { error: 'فشل في جلب إحصائيات العملاء' }\n      }\n\n      const customers = allCustomers || []\n      const companyKeywords = ['شركة', 'مؤسسة', 'مجموعة', 'شراكة', 'تجارة', 'خدمات']\n      \n      const companies = customers.filter(customer =>\n        companyKeywords.some(keyword => customer.full_name.includes(keyword))\n      ).length\n\n      const individuals = customers.length - companies\n\n      // Count customers added in the last 30 days\n      const thirtyDaysAgo = new Date()\n      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)\n      \n      const recentlyAdded = customers.filter(customer =>\n        new Date(customer.created_at) > thirtyDaysAgo\n      ).length\n\n      return {\n        data: {\n          total: customers.length,\n          companies,\n          individuals,\n          recentlyAdded\n        }\n      }\n    } catch (error) {\n      console.error('Unexpected error:', error)\n      return { error: 'حدث خطأ غير متوقع' }\n    }\n  }\n\n  // Get customers with their device count\n  static async getWithDeviceCount(): Promise<ApiResponse<(Customer & { device_count: number })[]>> {\n    try {\n      const { data, error } = await supabase\n        .from('customers')\n        .select(`\n          *,\n          devices(count)\n        `)\n        .order('created_at', { ascending: false })\n\n      if (error) {\n        console.error('Error fetching customers with device count:', error)\n        return { error: 'فشل في جلب بيانات العملاء' }\n      }\n\n      const customersWithCount = (data || []).map(customer => ({\n        ...customer,\n        device_count: customer.devices?.[0]?.count || 0\n      }))\n\n      return { data: customersWithCount }\n    } catch (error) {\n      console.error('Unexpected error:', error)\n      return { error: 'حدث خطأ غير متوقع' }\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAGO,MAAM;IACX,oBAAoB;IACpB,aAAa,SAA2C;QACtD,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,OAAO;oBAAE,OAAO;gBAA4B;YAC9C;YAEA,OAAO;gBAAE,MAAM,QAAQ,EAAE;YAAC;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO;gBAAE,OAAO;YAAoB;QACtC;IACF;IAEA,qBAAqB;IACrB,aAAa,QAAQ,EAAU,EAAkC;QAC/D,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,IACT,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,OAAO;oBAAE,OAAO;gBAA2B;YAC7C;YAEA,IAAI,CAAC,MAAM;gBACT,OAAO;oBAAE,OAAO;gBAAmB;YACrC;YAEA,OAAO;gBAAE;YAAK;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO;gBAAE,OAAO;YAAoB;QACtC;IACF;IAEA,sBAAsB;IACtB,aAAa,OAAO,YAA8B,EAAkC;QAClF,IAAI;YACF,uCAAuC;YACvC,MAAM,EAAE,MAAM,gBAAgB,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC9C,IAAI,CAAC,aACL,MAAM,CAAC,MACP,EAAE,CAAC,iBAAiB,aAAa,aAAa,EAC9C,MAAM;YAET,IAAI,kBAAkB;gBACpB,OAAO;oBAAE,OAAO;gBAAmC;YACrD;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC;gBAAC;aAAa,EACrB,MAAM,GACN,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,OAAO;oBAAE,OAAO;gBAAsB;YACxC;YAEA,OAAO;gBACL;gBACA,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO;gBAAE,OAAO;YAAoB;QACtC;IACF;IAEA,kBAAkB;IAClB,aAAa,OAAO,EAAU,EAAE,YAAuC,EAAkC;QACvG,IAAI;YACF,2EAA2E;YAC3E,IAAI,aAAa,aAAa,EAAE;gBAC9B,MAAM,EAAE,MAAM,gBAAgB,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC9C,IAAI,CAAC,aACL,MAAM,CAAC,MACP,EAAE,CAAC,iBAAiB,aAAa,aAAa,EAC9C,GAAG,CAAC,MAAM,IACV,MAAM;gBAET,IAAI,kBAAkB;oBACpB,OAAO;wBAAE,OAAO;oBAAmC;gBACrD;YACF;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC,cACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,OAAO;oBAAE,OAAO;gBAA6B;YAC/C;YAEA,OAAO;gBACL;gBACA,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO;gBAAE,OAAO;YAAoB;QACtC;IACF;IAEA,kBAAkB;IAClB,aAAa,OAAO,EAAU,EAA8B;QAC1D,IAAI;YACF,wDAAwD;YACxD,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACrC,IAAI,CAAC,WACL,MAAM,CAAC,MACP,EAAE,CAAC,eAAe,IAClB,KAAK,CAAC;YAET,IAAI,WAAW,QAAQ,MAAM,GAAG,GAAG;gBACjC,OAAO;oBAAE,OAAO;gBAAgD;YAClE;YAEA,MAAM,EAAE,MAAM,eAAe,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7C,IAAI,CAAC,oBACL,MAAM,CAAC,MACP,EAAE,CAAC,eAAe,IAClB,KAAK,CAAC;YAET,IAAI,mBAAmB,gBAAgB,MAAM,GAAG,GAAG;gBACjD,OAAO;oBAAE,OAAO;gBAAgD;YAClE;YAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,aACL,MAAM,GACN,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,OAAO;oBAAE,OAAO;gBAAoB;YACtC;YAEA,OAAO;gBAAE,SAAS;YAAsB;QAC1C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO;gBAAE,OAAO;YAAoB;QACtC;IACF;IAEA,mBAAmB;IACnB,aAAa,OAAO,KAAa,EAAoC;QACnE,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC,KACP,EAAE,CAAC,CAAC,iBAAiB,EAAE,MAAM,sBAAsB,EAAE,MAAM,eAAe,EAAE,MAAM,0BAA0B,EAAE,MAAM,CAAC,CAAC,EACtH,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,OAAO;oBAAE,OAAO;gBAA0B;YAC5C;YAEA,OAAO;gBAAE,MAAM,QAAQ,EAAE;YAAC;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO;gBAAE,OAAO;YAAoB;QACtC;IACF;IAEA,0BAA0B;IAC1B,aAAa,gBAKT;QACF,IAAI;YACF,MAAM,EAAE,MAAM,YAAY,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACjD,IAAI,CAAC,aACL,MAAM,CAAC;YAEV,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,uCAAuC;gBACrD,OAAO;oBAAE,OAAO;gBAA8B;YAChD;YAEA,MAAM,YAAY,gBAAgB,EAAE;YACpC,MAAM,kBAAkB;gBAAC;gBAAQ;gBAAS;gBAAU;gBAAS;gBAAS;aAAQ;YAE9E,MAAM,YAAY,UAAU,MAAM,CAAC,CAAA,WACjC,gBAAgB,IAAI,CAAC,CAAA,UAAW,SAAS,SAAS,CAAC,QAAQ,CAAC,WAC5D,MAAM;YAER,MAAM,cAAc,UAAU,MAAM,GAAG;YAEvC,4CAA4C;YAC5C,MAAM,gBAAgB,IAAI;YAC1B,cAAc,OAAO,CAAC,cAAc,OAAO,KAAK;YAEhD,MAAM,gBAAgB,UAAU,MAAM,CAAC,CAAA,WACrC,IAAI,KAAK,SAAS,UAAU,IAAI,eAChC,MAAM;YAER,OAAO;gBACL,MAAM;oBACJ,OAAO,UAAU,MAAM;oBACvB;oBACA;oBACA;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO;gBAAE,OAAO;YAAoB;QACtC;IACF;IAEA,wCAAwC;IACxC,aAAa,qBAAoF;QAC/F,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC,CAAC;;;QAGT,CAAC,EACA,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,+CAA+C;gBAC7D,OAAO;oBAAE,OAAO;gBAA4B;YAC9C;YAEA,MAAM,qBAAqB,CAAC,QAAQ,EAAE,EAAE,GAAG,CAAC,CAAA,WAAY,CAAC;oBACvD,GAAG,QAAQ;oBACX,cAAc,SAAS,OAAO,EAAE,CAAC,EAAE,EAAE,SAAS;gBAChD,CAAC;YAED,OAAO;gBAAE,MAAM;YAAmB;QACpC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO;gBAAE,OAAO;YAAoB;QACtC;IACF;AACF", "debugId": null}}, {"offset": {"line": 2715, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mY/AI/7-2025/CRM%20AGMENT/maintenance-crm/src/lib/api/devices.ts"], "sourcesContent": ["import { supabase } from '@/lib/supabase'\nimport { Device, DeviceFormData, ApiResponse } from '@/types'\n\nexport class DevicesAPI {\n  // Get all devices\n  static async getAll(): Promise<ApiResponse<Device[]>> {\n    try {\n      const { data, error } = await supabase\n        .from('devices')\n        .select(`\n          *,\n          customers(full_name, primary_phone)\n        `)\n        .order('created_at', { ascending: false })\n\n      if (error) {\n        console.error('Error fetching devices:', error)\n        return { error: 'فشل في جلب بيانات الأجهزة' }\n      }\n\n      return { data: data || [] }\n    } catch (error) {\n      console.error('Unexpected error:', error)\n      return { error: 'حدث خطأ غير متوقع' }\n    }\n  }\n\n  // Get devices by customer ID\n  static async getByCustomerId(customerId: string): Promise<ApiResponse<Device[]>> {\n    try {\n      const { data, error } = await supabase\n        .from('devices')\n        .select('*')\n        .eq('customer_id', customerId)\n        .order('created_at', { ascending: false })\n\n      if (error) {\n        console.error('Error fetching customer devices:', error)\n        return { error: 'فشل في جلب أجهزة العميل' }\n      }\n\n      return { data: data || [] }\n    } catch (error) {\n      console.error('Unexpected error:', error)\n      return { error: 'حدث خطأ غير متوقع' }\n    }\n  }\n\n  // Get device by ID\n  static async getById(id: string): Promise<ApiResponse<Device>> {\n    try {\n      const { data, error } = await supabase\n        .from('devices')\n        .select(`\n          *,\n          customers(full_name, primary_phone, detailed_address)\n        `)\n        .eq('id', id)\n        .single()\n\n      if (error) {\n        console.error('Error fetching device:', error)\n        return { error: 'فشل في جلب بيانات الجهاز' }\n      }\n\n      if (!data) {\n        return { error: 'الجهاز غير موجود' }\n      }\n\n      return { data }\n    } catch (error) {\n      console.error('Unexpected error:', error)\n      return { error: 'حدث خطأ غير متوقع' }\n    }\n  }\n\n  // Create new device\n  static async create(deviceData: DeviceFormData & { customer_id: string }): Promise<ApiResponse<Device>> {\n    try {\n      // Check if serial number already exists\n      const { data: existingDevice } = await supabase\n        .from('devices')\n        .select('id')\n        .eq('serial_number', deviceData.serial_number)\n        .single()\n\n      if (existingDevice) {\n        return { error: 'الرقم التسلسلي مسجل مسبقاً لجهاز آخر' }\n      }\n\n      // Verify customer exists\n      const { data: customer } = await supabase\n        .from('customers')\n        .select('id')\n        .eq('id', deviceData.customer_id)\n        .single()\n\n      if (!customer) {\n        return { error: 'العميل غير موجود' }\n      }\n\n      const { data, error } = await supabase\n        .from('devices')\n        .insert([deviceData])\n        .select()\n        .single()\n\n      if (error) {\n        console.error('Error creating device:', error)\n        return { error: 'فشل في إنشاء الجهاز' }\n      }\n\n      return { \n        data, \n        message: 'تم إنشاء الجهاز بنجاح' \n      }\n    } catch (error) {\n      console.error('Unexpected error:', error)\n      return { error: 'حدث خطأ غير متوقع' }\n    }\n  }\n\n  // Update device\n  static async update(id: string, deviceData: Partial<DeviceFormData>): Promise<ApiResponse<Device>> {\n    try {\n      // If updating serial number, check if it's already used by another device\n      if (deviceData.serial_number) {\n        const { data: existingDevice } = await supabase\n          .from('devices')\n          .select('id')\n          .eq('serial_number', deviceData.serial_number)\n          .neq('id', id)\n          .single()\n\n        if (existingDevice) {\n          return { error: 'الرقم التسلسلي مسجل مسبقاً لجهاز آخر' }\n        }\n      }\n\n      const { data, error } = await supabase\n        .from('devices')\n        .update(deviceData)\n        .eq('id', id)\n        .select()\n        .single()\n\n      if (error) {\n        console.error('Error updating device:', error)\n        return { error: 'فشل في تحديث بيانات الجهاز' }\n      }\n\n      return { \n        data, \n        message: 'تم تحديث بيانات الجهاز بنجاح' \n      }\n    } catch (error) {\n      console.error('Unexpected error:', error)\n      return { error: 'حدث خطأ غير متوقع' }\n    }\n  }\n\n  // Delete device\n  static async delete(id: string): Promise<ApiResponse<void>> {\n    try {\n      // Check if device has any service requests\n      const { data: serviceRequests } = await supabase\n        .from('service_requests')\n        .select('id')\n        .eq('device_id', id)\n        .limit(1)\n\n      if (serviceRequests && serviceRequests.length > 0) {\n        return { error: 'لا يمكن حذف الجهاز لأنه يحتوي على طلبات صيانة' }\n      }\n\n      const { error } = await supabase\n        .from('devices')\n        .delete()\n        .eq('id', id)\n\n      if (error) {\n        console.error('Error deleting device:', error)\n        return { error: 'فشل في حذف الجهاز' }\n      }\n\n      return { message: 'تم حذف الجهاز بنجاح' }\n    } catch (error) {\n      console.error('Unexpected error:', error)\n      return { error: 'حدث خطأ غير متوقع' }\n    }\n  }\n\n  // Search devices\n  static async search(query: string): Promise<ApiResponse<Device[]>> {\n    try {\n      const { data, error } = await supabase\n        .from('devices')\n        .select(`\n          *,\n          customers(full_name, primary_phone)\n        `)\n        .or(`device_name.ilike.%${query}%,model.ilike.%${query}%,serial_number.ilike.%${query}%`)\n        .order('created_at', { ascending: false })\n\n      if (error) {\n        console.error('Error searching devices:', error)\n        return { error: 'فشل في البحث عن الأجهزة' }\n      }\n\n      return { data: data || [] }\n    } catch (error) {\n      console.error('Unexpected error:', error)\n      return { error: 'حدث خطأ غير متوقع' }\n    }\n  }\n\n  // Get device statistics\n  static async getStatistics(): Promise<ApiResponse<{\n    total: number\n    underWarranty: number\n    expiredWarranty: number\n    noWarrantyInfo: number\n  }>> {\n    try {\n      const { data: allDevices, error } = await supabase\n        .from('devices')\n        .select('warranty_end_date')\n\n      if (error) {\n        console.error('Error fetching device statistics:', error)\n        return { error: 'فشل في جلب إحصائيات الأجهزة' }\n      }\n\n      const devices = allDevices || []\n      const today = new Date()\n      \n      let underWarranty = 0\n      let expiredWarranty = 0\n      let noWarrantyInfo = 0\n\n      devices.forEach(device => {\n        if (!device.warranty_end_date) {\n          noWarrantyInfo++\n        } else {\n          const warrantyEndDate = new Date(device.warranty_end_date)\n          if (warrantyEndDate > today) {\n            underWarranty++\n          } else {\n            expiredWarranty++\n          }\n        }\n      })\n\n      return {\n        data: {\n          total: devices.length,\n          underWarranty,\n          expiredWarranty,\n          noWarrantyInfo\n        }\n      }\n    } catch (error) {\n      console.error('Unexpected error:', error)\n      return { error: 'حدث خطأ غير متوقع' }\n    }\n  }\n\n  // Get devices with warranty status\n  static async getWithWarrantyStatus(): Promise<ApiResponse<(Device & { warranty_status: 'active' | 'expired' | 'unknown' })[]>> {\n    try {\n      const { data, error } = await supabase\n        .from('devices')\n        .select(`\n          *,\n          customers(full_name, primary_phone)\n        `)\n        .order('created_at', { ascending: false })\n\n      if (error) {\n        console.error('Error fetching devices with warranty status:', error)\n        return { error: 'فشل في جلب بيانات الأجهزة' }\n      }\n\n      const today = new Date()\n      const devicesWithStatus = (data || []).map(device => {\n        let warranty_status: 'active' | 'expired' | 'unknown' = 'unknown'\n        \n        if (device.warranty_end_date) {\n          const warrantyEndDate = new Date(device.warranty_end_date)\n          warranty_status = warrantyEndDate > today ? 'active' : 'expired'\n        }\n\n        return {\n          ...device,\n          warranty_status\n        }\n      })\n\n      return { data: devicesWithStatus }\n    } catch (error) {\n      console.error('Unexpected error:', error)\n      return { error: 'حدث خطأ غير متوقع' }\n    }\n  }\n\n  // Get devices expiring soon (within next 30 days)\n  static async getExpiringSoon(): Promise<ApiResponse<Device[]>> {\n    try {\n      const today = new Date()\n      const thirtyDaysFromNow = new Date()\n      thirtyDaysFromNow.setDate(today.getDate() + 30)\n\n      const { data, error } = await supabase\n        .from('devices')\n        .select(`\n          *,\n          customers(full_name, primary_phone)\n        `)\n        .gte('warranty_end_date', today.toISOString().split('T')[0])\n        .lte('warranty_end_date', thirtyDaysFromNow.toISOString().split('T')[0])\n        .order('warranty_end_date', { ascending: true })\n\n      if (error) {\n        console.error('Error fetching devices expiring soon:', error)\n        return { error: 'فشل في جلب الأجهزة منتهية الضمان قريباً' }\n      }\n\n      return { data: data || [] }\n    } catch (error) {\n      console.error('Unexpected error:', error)\n      return { error: 'حدث خطأ غير متوقع' }\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAGO,MAAM;IACX,kBAAkB;IAClB,aAAa,SAAyC;QACpD,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC,CAAC;;;QAGT,CAAC,EACA,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,OAAO;oBAAE,OAAO;gBAA4B;YAC9C;YAEA,OAAO;gBAAE,MAAM,QAAQ,EAAE;YAAC;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO;gBAAE,OAAO;YAAoB;QACtC;IACF;IAEA,6BAA6B;IAC7B,aAAa,gBAAgB,UAAkB,EAAkC;QAC/E,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC,KACP,EAAE,CAAC,eAAe,YAClB,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,OAAO;oBAAE,OAAO;gBAA0B;YAC5C;YAEA,OAAO;gBAAE,MAAM,QAAQ,EAAE;YAAC;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO;gBAAE,OAAO;YAAoB;QACtC;IACF;IAEA,mBAAmB;IACnB,aAAa,QAAQ,EAAU,EAAgC;QAC7D,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC,CAAC;;;QAGT,CAAC,EACA,EAAE,CAAC,MAAM,IACT,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,OAAO;oBAAE,OAAO;gBAA2B;YAC7C;YAEA,IAAI,CAAC,MAAM;gBACT,OAAO;oBAAE,OAAO;gBAAmB;YACrC;YAEA,OAAO;gBAAE;YAAK;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO;gBAAE,OAAO;YAAoB;QACtC;IACF;IAEA,oBAAoB;IACpB,aAAa,OAAO,UAAoD,EAAgC;QACtG,IAAI;YACF,wCAAwC;YACxC,MAAM,EAAE,MAAM,cAAc,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC5C,IAAI,CAAC,WACL,MAAM,CAAC,MACP,EAAE,CAAC,iBAAiB,WAAW,aAAa,EAC5C,MAAM;YAET,IAAI,gBAAgB;gBAClB,OAAO;oBAAE,OAAO;gBAAuC;YACzD;YAEA,yBAAyB;YACzB,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACtC,IAAI,CAAC,aACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,WAAW,WAAW,EAC/B,MAAM;YAET,IAAI,CAAC,UAAU;gBACb,OAAO;oBAAE,OAAO;gBAAmB;YACrC;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC;gBAAC;aAAW,EACnB,MAAM,GACN,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,OAAO;oBAAE,OAAO;gBAAsB;YACxC;YAEA,OAAO;gBACL;gBACA,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO;gBAAE,OAAO;YAAoB;QACtC;IACF;IAEA,gBAAgB;IAChB,aAAa,OAAO,EAAU,EAAE,UAAmC,EAAgC;QACjG,IAAI;YACF,0EAA0E;YAC1E,IAAI,WAAW,aAAa,EAAE;gBAC5B,MAAM,EAAE,MAAM,cAAc,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC5C,IAAI,CAAC,WACL,MAAM,CAAC,MACP,EAAE,CAAC,iBAAiB,WAAW,aAAa,EAC5C,GAAG,CAAC,MAAM,IACV,MAAM;gBAET,IAAI,gBAAgB;oBAClB,OAAO;wBAAE,OAAO;oBAAuC;gBACzD;YACF;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,OAAO;oBAAE,OAAO;gBAA6B;YAC/C;YAEA,OAAO;gBACL;gBACA,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO;gBAAE,OAAO;YAAoB;QACtC;IACF;IAEA,gBAAgB;IAChB,aAAa,OAAO,EAAU,EAA8B;QAC1D,IAAI;YACF,2CAA2C;YAC3C,MAAM,EAAE,MAAM,eAAe,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7C,IAAI,CAAC,oBACL,MAAM,CAAC,MACP,EAAE,CAAC,aAAa,IAChB,KAAK,CAAC;YAET,IAAI,mBAAmB,gBAAgB,MAAM,GAAG,GAAG;gBACjD,OAAO;oBAAE,OAAO;gBAAgD;YAClE;YAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,WACL,MAAM,GACN,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,OAAO;oBAAE,OAAO;gBAAoB;YACtC;YAEA,OAAO;gBAAE,SAAS;YAAsB;QAC1C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO;gBAAE,OAAO;YAAoB;QACtC;IACF;IAEA,iBAAiB;IACjB,aAAa,OAAO,KAAa,EAAkC;QACjE,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC,CAAC;;;QAGT,CAAC,EACA,EAAE,CAAC,CAAC,mBAAmB,EAAE,MAAM,eAAe,EAAE,MAAM,uBAAuB,EAAE,MAAM,CAAC,CAAC,EACvF,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,OAAO;oBAAE,OAAO;gBAA0B;YAC5C;YAEA,OAAO;gBAAE,MAAM,QAAQ,EAAE;YAAC;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO;gBAAE,OAAO;YAAoB;QACtC;IACF;IAEA,wBAAwB;IACxB,aAAa,gBAKT;QACF,IAAI;YACF,MAAM,EAAE,MAAM,UAAU,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC/C,IAAI,CAAC,WACL,MAAM,CAAC;YAEV,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,qCAAqC;gBACnD,OAAO;oBAAE,OAAO;gBAA8B;YAChD;YAEA,MAAM,UAAU,cAAc,EAAE;YAChC,MAAM,QAAQ,IAAI;YAElB,IAAI,gBAAgB;YACpB,IAAI,kBAAkB;YACtB,IAAI,iBAAiB;YAErB,QAAQ,OAAO,CAAC,CAAA;gBACd,IAAI,CAAC,OAAO,iBAAiB,EAAE;oBAC7B;gBACF,OAAO;oBACL,MAAM,kBAAkB,IAAI,KAAK,OAAO,iBAAiB;oBACzD,IAAI,kBAAkB,OAAO;wBAC3B;oBACF,OAAO;wBACL;oBACF;gBACF;YACF;YAEA,OAAO;gBACL,MAAM;oBACJ,OAAO,QAAQ,MAAM;oBACrB;oBACA;oBACA;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO;gBAAE,OAAO;YAAoB;QACtC;IACF;IAEA,mCAAmC;IACnC,aAAa,wBAAkH;QAC7H,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC,CAAC;;;QAGT,CAAC,EACA,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,gDAAgD;gBAC9D,OAAO;oBAAE,OAAO;gBAA4B;YAC9C;YAEA,MAAM,QAAQ,IAAI;YAClB,MAAM,oBAAoB,CAAC,QAAQ,EAAE,EAAE,GAAG,CAAC,CAAA;gBACzC,IAAI,kBAAoD;gBAExD,IAAI,OAAO,iBAAiB,EAAE;oBAC5B,MAAM,kBAAkB,IAAI,KAAK,OAAO,iBAAiB;oBACzD,kBAAkB,kBAAkB,QAAQ,WAAW;gBACzD;gBAEA,OAAO;oBACL,GAAG,MAAM;oBACT;gBACF;YACF;YAEA,OAAO;gBAAE,MAAM;YAAkB;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO;gBAAE,OAAO;YAAoB;QACtC;IACF;IAEA,kDAAkD;IAClD,aAAa,kBAAkD;QAC7D,IAAI;YACF,MAAM,QAAQ,IAAI;YAClB,MAAM,oBAAoB,IAAI;YAC9B,kBAAkB,OAAO,CAAC,MAAM,OAAO,KAAK;YAE5C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC,CAAC;;;QAGT,CAAC,EACA,GAAG,CAAC,qBAAqB,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAC1D,GAAG,CAAC,qBAAqB,kBAAkB,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EACtE,KAAK,CAAC,qBAAqB;gBAAE,WAAW;YAAK;YAEhD,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,yCAAyC;gBACvD,OAAO;oBAAE,OAAO;gBAA0C;YAC5D;YAEA,OAAO;gBAAE,MAAM,QAAQ,EAAE;YAAC;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO;gBAAE,OAAO;YAAoB;QACtC;IACF;AACF", "debugId": null}}, {"offset": {"line": 3032, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mY/AI/7-2025/CRM%20AGMENT/maintenance-crm/src/app/customers/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { <PERSON><PERSON>, Devi<PERSON>, ServiceRequest } from '@/types'\nimport { CustomerList } from '@/components/customers/customer-list'\nimport { CustomerForm } from '@/components/customers/customer-form'\nimport { CustomerDetails } from '@/components/customers/customer-details'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { ArrowRight, Users } from 'lucide-react'\nimport { CustomersAPI } from '@/lib/api/customers'\nimport { DevicesAPI } from '@/lib/api/devices'\nimport Link from 'next/link'\n\ntype ViewMode = 'list' | 'add' | 'edit' | 'view'\n\nexport default function CustomersPage() {\n  const [customers, setCustomers] = useState<Customer[]>([])\n  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null)\n  const [customerDevices, setCustomerDevices] = useState<Device[]>([])\n  const [customerRequests, setCustomerRequests] = useState<ServiceRequest[]>([])\n  const [viewMode, setViewMode] = useState<ViewMode>('list')\n  const [isLoading, setIsLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n\n  // Load customers data\n  useEffect(() => {\n    loadCustomers()\n  }, [])\n\n  const loadCustomers = async () => {\n    setIsLoading(true)\n    setError(null)\n\n    const result = await CustomersAPI.getAll()\n\n    if (result.error) {\n      setError(result.error)\n      // Fallback to mock data for development\n      const mockCustomers: Customer[] = [\n        {\n          id: '1',\n          full_name: 'شركة الرياض للتجارة',\n          primary_phone: '+966112345678',\n          secondary_phone: '+966112345679',\n          email: '<EMAIL>',\n          detailed_address: 'شارع الملك فهد، حي العليا، الرياض',\n          landmark: 'بجانب برج المملكة',\n          contact_person: 'أحمد المدير العام',\n          special_notes: 'يفضل الاتصال صباحاً',\n          created_at: '2024-01-15T10:00:00Z',\n          updated_at: '2024-01-15T10:00:00Z'\n        },\n        {\n          id: '2',\n          full_name: 'عبدالرحمن محمد الغامدي',\n          primary_phone: '+966501111111',\n          secondary_phone: '+966502222222',\n          email: '<EMAIL>',\n          detailed_address: 'حي النرجس، شارع التخصصي، الرياض',\n          landmark: 'فيلا رقم 123',\n          contact_person: undefined,\n          special_notes: 'لديه حساسية من الغبار',\n          created_at: '2024-02-10T14:30:00Z',\n          updated_at: '2024-02-10T14:30:00Z'\n        }\n      ]\n      setCustomers(mockCustomers)\n    } else {\n      setCustomers(result.data || [])\n    }\n\n    setIsLoading(false)\n  }\n\n  const loadCustomerDetails = async (customerId: string) => {\n    // Load customer devices\n    const devicesResult = await DevicesAPI.getByCustomerId(customerId)\n    if (devicesResult.data) {\n      setCustomerDevices(devicesResult.data)\n    }\n\n    // Load customer service requests (will be implemented later)\n    setCustomerRequests([])\n  }\n\n  const handleAddCustomer = async (data: any) => {\n    const result = await CustomersAPI.create(data)\n\n    if (result.error) {\n      alert(`خطأ: ${result.error}`)\n      return\n    }\n\n    if (result.data) {\n      setCustomers(prev => [result.data!, ...prev])\n      setViewMode('list')\n      alert(result.message || 'تم إضافة العميل بنجاح!')\n    }\n  }\n\n  const handleEditCustomer = async (data: any) => {\n    if (!selectedCustomer) return\n\n    const result = await CustomersAPI.update(selectedCustomer.id, data)\n\n    if (result.error) {\n      alert(`خطأ: ${result.error}`)\n      return\n    }\n\n    if (result.data) {\n      setCustomers(prev =>\n        prev.map(customer =>\n          customer.id === selectedCustomer.id ? result.data! : customer\n        )\n      )\n      setSelectedCustomer(result.data)\n      setViewMode('view')\n      alert(result.message || 'تم تحديث بيانات العميل بنجاح!')\n    }\n  }\n\n  const handleViewCustomer = async (customer: Customer) => {\n    setSelectedCustomer(customer)\n    await loadCustomerDetails(customer.id)\n    setViewMode('view')\n  }\n\n  const handleEditMode = (customer: Customer) => {\n    setSelectedCustomer(customer)\n    setViewMode('edit')\n  }\n\n  const handleBackToList = () => {\n    setSelectedCustomer(null)\n    setViewMode('list')\n  }\n\n  const renderContent = () => {\n    switch (viewMode) {\n      case 'add':\n        return (\n          <CustomerForm\n            onSubmit={handleAddCustomer}\n            onCancel={handleBackToList}\n          />\n        )\n      \n      case 'edit':\n        return selectedCustomer ? (\n          <CustomerForm\n            initialData={selectedCustomer}\n            onSubmit={handleEditCustomer}\n            onCancel={handleBackToList}\n          />\n        ) : null\n      \n      case 'view':\n        return selectedCustomer ? (\n          <CustomerDetails\n            customer={selectedCustomer}\n            devices={customerDevices}\n            recentRequests={customerRequests}\n            onEdit={() => setViewMode('edit')}\n            onAddDevice={() => {\n              // سيتم تنفيذ هذا لاحقاً\n              alert('سيتم إضافة هذه الميزة قريباً')\n            }}\n            onAddServiceRequest={() => {\n              // سيتم تنفيذ هذا لاحقاً\n              alert('سيتم إضافة هذه الميزة قريباً')\n            }}\n          />\n        ) : null\n      \n      default:\n        return (\n          <CustomerList\n            customers={customers}\n            onEdit={handleEditMode}\n            onView={handleViewCustomer}\n            onAdd={() => setViewMode('add')}\n            isLoading={isLoading}\n          />\n        )\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <div className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between py-6\">\n            <div className=\"flex items-center gap-4\">\n              {viewMode !== 'list' && (\n                <Button\n                  variant=\"ghost\"\n                  onClick={handleBackToList}\n                  className=\"flex items-center gap-2\"\n                >\n                  <ArrowRight className=\"h-4 w-4\" />\n                  العودة للقائمة\n                </Button>\n              )}\n              <div className=\"flex items-center gap-3\">\n                <Users className=\"h-8 w-8 text-blue-600\" />\n                <div>\n                  <h1 className=\"text-2xl font-bold text-gray-900\">\n                    {viewMode === 'add' && 'إضافة عميل جديد'}\n                    {viewMode === 'edit' && 'تعديل بيانات العميل'}\n                    {viewMode === 'view' && 'تفاصيل العميل'}\n                    {viewMode === 'list' && 'إدارة العملاء'}\n                  </h1>\n                  <p className=\"text-gray-600\">\n                    {viewMode === 'add' && 'أدخل بيانات العميل الجديد'}\n                    {viewMode === 'edit' && 'قم بتعديل بيانات العميل'}\n                    {viewMode === 'view' && selectedCustomer?.full_name}\n                    {viewMode === 'list' && 'عرض وإدارة جميع العملاء في النظام'}\n                  </p>\n                </div>\n              </div>\n            </div>\n            <div className=\"flex items-center gap-4\">\n              <Link href=\"/\">\n                <Button variant=\"outline\">\n                  العودة للرئيسية\n                </Button>\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Statistics Cards - only show in list view */}\n        {viewMode === 'list' && !isLoading && (\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium\">إجمالي العملاء</CardTitle>\n                <Users className=\"h-4 w-4 text-muted-foreground\" />\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold\">{customers.length}</div>\n                <p className=\"text-xs text-muted-foreground\">\n                  عميل مسجل في النظام\n                </p>\n              </CardContent>\n            </Card>\n            \n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium\">المؤسسات</CardTitle>\n                <Users className=\"h-4 w-4 text-muted-foreground\" />\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold\">\n                  {customers.filter(c => \n                    ['شركة', 'مؤسسة', 'مجموعة'].some(keyword => \n                      c.full_name.includes(keyword)\n                    )\n                  ).length}\n                </div>\n                <p className=\"text-xs text-muted-foreground\">\n                  مؤسسة وشركة\n                </p>\n              </CardContent>\n            </Card>\n            \n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium\">الأفراد</CardTitle>\n                <Users className=\"h-4 w-4 text-muted-foreground\" />\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold\">\n                  {customers.filter(c => \n                    !['شركة', 'مؤسسة', 'مجموعة'].some(keyword => \n                      c.full_name.includes(keyword)\n                    )\n                  ).length}\n                </div>\n                <p className=\"text-xs text-muted-foreground\">\n                  عميل فردي\n                </p>\n              </CardContent>\n            </Card>\n          </div>\n        )}\n\n        {/* Main Content Area */}\n        {renderContent()}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;;;AAZA;;;;;;;;;;;AAgBe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IAC7E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,sBAAsB;IACtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,aAAa;QACb,SAAS;QAET,MAAM,SAAS,MAAM,iIAAA,CAAA,eAAY,CAAC,MAAM;QAExC,IAAI,OAAO,KAAK,EAAE;YAChB,SAAS,OAAO,KAAK;YACrB,wCAAwC;YACxC,MAAM,gBAA4B;gBAChC;oBACE,IAAI;oBACJ,WAAW;oBACX,eAAe;oBACf,iBAAiB;oBACjB,OAAO;oBACP,kBAAkB;oBAClB,UAAU;oBACV,gBAAgB;oBAChB,eAAe;oBACf,YAAY;oBACZ,YAAY;gBACd;gBACA;oBACE,IAAI;oBACJ,WAAW;oBACX,eAAe;oBACf,iBAAiB;oBACjB,OAAO;oBACP,kBAAkB;oBAClB,UAAU;oBACV,gBAAgB;oBAChB,eAAe;oBACf,YAAY;oBACZ,YAAY;gBACd;aACD;YACD,aAAa;QACf,OAAO;YACL,aAAa,OAAO,IAAI,IAAI,EAAE;QAChC;QAEA,aAAa;IACf;IAEA,MAAM,sBAAsB,OAAO;QACjC,wBAAwB;QACxB,MAAM,gBAAgB,MAAM,+HAAA,CAAA,aAAU,CAAC,eAAe,CAAC;QACvD,IAAI,cAAc,IAAI,EAAE;YACtB,mBAAmB,cAAc,IAAI;QACvC;QAEA,6DAA6D;QAC7D,oBAAoB,EAAE;IACxB;IAEA,MAAM,oBAAoB,OAAO;QAC/B,MAAM,SAAS,MAAM,iIAAA,CAAA,eAAY,CAAC,MAAM,CAAC;QAEzC,IAAI,OAAO,KAAK,EAAE;YAChB,MAAM,CAAC,KAAK,EAAE,OAAO,KAAK,EAAE;YAC5B;QACF;QAEA,IAAI,OAAO,IAAI,EAAE;YACf,aAAa,CAAA,OAAQ;oBAAC,OAAO,IAAI;uBAAM;iBAAK;YAC5C,YAAY;YACZ,MAAM,OAAO,OAAO,IAAI;QAC1B;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI,CAAC,kBAAkB;QAEvB,MAAM,SAAS,MAAM,iIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,iBAAiB,EAAE,EAAE;QAE9D,IAAI,OAAO,KAAK,EAAE;YAChB,MAAM,CAAC,KAAK,EAAE,OAAO,KAAK,EAAE;YAC5B;QACF;QAEA,IAAI,OAAO,IAAI,EAAE;YACf,aAAa,CAAA,OACX,KAAK,GAAG,CAAC,CAAA,WACP,SAAS,EAAE,KAAK,iBAAiB,EAAE,GAAG,OAAO,IAAI,GAAI;YAGzD,oBAAoB,OAAO,IAAI;YAC/B,YAAY;YACZ,MAAM,OAAO,OAAO,IAAI;QAC1B;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,oBAAoB;QACpB,MAAM,oBAAoB,SAAS,EAAE;QACrC,YAAY;IACd;IAEA,MAAM,iBAAiB,CAAC;QACtB,oBAAoB;QACpB,YAAY;IACd;IAEA,MAAM,mBAAmB;QACvB,oBAAoB;QACpB,YAAY;IACd;IAEA,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,qBACE,6LAAC,sJAAA,CAAA,eAAY;oBACX,UAAU;oBACV,UAAU;;;;;;YAIhB,KAAK;gBACH,OAAO,iCACL,6LAAC,sJAAA,CAAA,eAAY;oBACX,aAAa;oBACb,UAAU;oBACV,UAAU;;;;;2BAEV;YAEN,KAAK;gBACH,OAAO,iCACL,6LAAC,yJAAA,CAAA,kBAAe;oBACd,UAAU;oBACV,SAAS;oBACT,gBAAgB;oBAChB,QAAQ,IAAM,YAAY;oBAC1B,aAAa;wBACX,wBAAwB;wBACxB,MAAM;oBACR;oBACA,qBAAqB;wBACnB,wBAAwB;wBACxB,MAAM;oBACR;;;;;2BAEA;YAEN;gBACE,qBACE,6LAAC,sJAAA,CAAA,eAAY;oBACX,WAAW;oBACX,QAAQ;oBACR,QAAQ;oBACR,OAAO,IAAM,YAAY;oBACzB,WAAW;;;;;;QAGnB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCACZ,aAAa,wBACZ,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS;wCACT,WAAU;;0DAEV,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAItC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;;4DACX,aAAa,SAAS;4DACtB,aAAa,UAAU;4DACvB,aAAa,UAAU;4DACvB,aAAa,UAAU;;;;;;;kEAE1B,6LAAC;wDAAE,WAAU;;4DACV,aAAa,SAAS;4DACtB,aAAa,UAAU;4DACvB,aAAa,UAAU,kBAAkB;4DACzC,aAAa,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;0CAKhC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUpC,6LAAC;gBAAI,WAAU;;oBAEZ,aAAa,UAAU,CAAC,2BACvB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;;kDAEnB,6LAAC,mIAAA,CAAA,cAAW;;0DACV,6LAAC;gDAAI,WAAU;0DAAsB,UAAU,MAAM;;;;;;0DACrD,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;0CAMjD,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;;kDAEnB,6LAAC,mIAAA,CAAA,cAAW;;0DACV,6LAAC;gDAAI,WAAU;0DACZ,UAAU,MAAM,CAAC,CAAA,IAChB;wDAAC;wDAAQ;wDAAS;qDAAS,CAAC,IAAI,CAAC,CAAA,UAC/B,EAAE,SAAS,CAAC,QAAQ,CAAC,WAEvB,MAAM;;;;;;0DAEV,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;0CAMjD,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;;kDAEnB,6LAAC,mIAAA,CAAA,cAAW;;0DACV,6LAAC;gDAAI,WAAU;0DACZ,UAAU,MAAM,CAAC,CAAA,IAChB,CAAC;wDAAC;wDAAQ;wDAAS;qDAAS,CAAC,IAAI,CAAC,CAAA,UAChC,EAAE,SAAS,CAAC,QAAQ,CAAC,WAEvB,MAAM;;;;;;0DAEV,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;;;;;;;oBASpD;;;;;;;;;;;;;AAIT;GA1RwB;KAAA", "debugId": null}}]}