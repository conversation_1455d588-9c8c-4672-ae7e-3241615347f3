// Customer Types
export interface Customer {
  id: string
  full_name: string
  primary_phone: string
  secondary_phone?: string
  email?: string
  detailed_address: string
  landmark?: string
  contact_person?: string
  special_notes?: string
  created_at: string
  updated_at: string
}

// Device Types
export interface Device {
  id: string
  customer_id: string
  device_name: string
  model: string
  serial_number: string
  purchase_date?: string
  warranty_end_date?: string
  original_invoice_number?: string
  created_at: string
  updated_at: string
}

// Engineer Types
export interface Engineer {
  id: string
  full_name: string
  phone: string
  email?: string
  specialization?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

// Service Request Types
export type Priority = 'عاجل' | 'متوسط' | 'منخفض'
export type RequestStatus = 
  | 'قيد المراجعة' 
  | 'تم تسجيله' 
  | 'في انتظار فني' 
  | 'تم تحديد موعد' 
  | 'في الطريق للعميل' 
  | 'قيد التنفيذ' 
  | 'مكتمل' 
  | 'مغلق'

export interface ServiceRequest {
  id: string
  customer_id: string
  device_id: string
  request_date: string
  received_by: string
  request_source: string
  initial_description: string
  priority: Priority
  status: RequestStatus
  created_at: string
  updated_at: string
  customer?: Customer
  device?: Device
}

// Scheduling Types
export interface Scheduling {
  id: string
  service_request_id: string
  engineer_id: string
  scheduled_date: string
  estimated_duration: number
  engineer_notes?: string
  status: string
  created_at: string
  updated_at: string
  engineer?: Engineer
  service_request?: ServiceRequest
}

// Execution Types
export interface Execution {
  id: string
  service_request_id: string
  engineer_id: string
  arrival_time?: string
  start_time?: string
  end_time?: string
  technical_description?: string
  repair_actions?: string
  failure_cause?: string
  tools_used?: string
  future_recommendations?: string
  received_by_customer?: string
  device_status_after?: string
  customer_signature?: string
  technical_report?: string
  created_at: string
  updated_at: string
  engineer?: Engineer
  service_request?: ServiceRequest
}

// Parts Types
export interface Part {
  id: string
  part_name: string
  part_number: string
  description?: string
  unit_price: number
  stock_quantity: number
  created_at: string
  updated_at: string
}

// Execution Parts Types
export interface ExecutionPart {
  id: string
  execution_id: string
  part_id: string
  quantity_used: number
  unit_price_at_time: number
  created_at: string
  part?: Part
}

// Follow-up Types
export interface FollowUp {
  id: string
  service_request_id: string
  invoice_number?: string
  payment_status: string
  closure_date?: string
  customer_service_notes?: string
  customer_rating?: number
  customer_feedback?: string
  engineer_performance_rating?: number
  internal_notes?: string
  created_at: string
  updated_at: string
  service_request?: ServiceRequest
}

// Invoice Types
export interface Invoice {
  id: string
  service_request_id: string
  invoice_number: string
  service_cost: number
  parts_cost: number
  total_amount: number
  payment_status: string
  issue_date: string
  payment_date?: string
  created_at: string
  updated_at: string
  service_request?: ServiceRequest
}

// Form Types
export interface CustomerFormData {
  full_name: string
  primary_phone: string
  secondary_phone?: string
  email?: string
  detailed_address: string
  landmark?: string
  contact_person?: string
  special_notes?: string
}

export interface DeviceFormData {
  device_name: string
  model: string
  serial_number: string
  purchase_date?: string
  warranty_end_date?: string
  original_invoice_number?: string
}

export interface ServiceRequestFormData {
  customer_id: string
  device_id: string
  received_by: string
  request_source: string
  initial_description: string
  priority: Priority
}

// API Response Types
export interface ApiResponse<T> {
  data?: T
  error?: string
  message?: string
}

// Dashboard Types
export interface DashboardStats {
  total_requests: number
  pending_requests: number
  completed_requests: number
  active_engineers: number
  customer_satisfaction: number
  average_resolution_time: number
}

// Search and Filter Types
export interface SearchFilters {
  search?: string
  status?: RequestStatus
  priority?: Priority
  engineer_id?: string
  date_from?: string
  date_to?: string
}
